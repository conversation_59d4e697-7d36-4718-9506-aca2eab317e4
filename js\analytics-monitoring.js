/**
 * Analytics and Monitoring System
 * Comprehensive analytics, performance monitoring, and SEO tracking
 */

class AnalyticsMonitoringSystem {
  constructor() {
    this.analytics = {
      pageViews: new Map(),
      events: [],
      userSessions: new Map(),
      performanceMetrics: new Map(),
      seoMetrics: new Map(),
      conversionFunnels: new Map()
    };
    this.config = {
      googleAnalyticsId: 'G-XXXXXXXXXX', // Replace with actual GA4 ID
      enableRealTimeMonitoring: true,
      enableHeatmaps: true,
      enableUserRecording: false, // Privacy consideration
      enableSEOMonitoring: true
    };
    this.init();
  }

  init() {
    this.initializeGoogleAnalytics();
    this.setupCustomEventTracking();
    this.initializePerformanceMonitoring();
    this.setupUserBehaviorTracking();
    this.initializeSEOMonitoring();
    this.createAnalyticsDashboard();
    this.startRealTimeMonitoring();
  }

  initializeGoogleAnalytics() {
    // Initialize Google Analytics 4
    if (typeof gtag === 'undefined') {
      // Load Google Analytics script
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${this.config.googleAnalyticsId}`;
      document.head.appendChild(script);

      // Initialize gtag
      window.dataLayer = window.dataLayer || [];
      window.gtag = function() {
        dataLayer.push(arguments);
      };

      gtag('js', new Date());
      gtag('config', this.config.googleAnalyticsId, {
        page_title: document.title,
        page_location: window.location.href,
        custom_map: {
          'custom_parameter_1': 'organization_type',
          'custom_parameter_2': 'user_engagement_level'
        }
      });
    }

    // Track initial page view
    this.trackPageView();
  }

  setupCustomEventTracking() {
    // Track donation events
    document.addEventListener('submit', (e) => {
      if (e.target.classList.contains('donation-form')) {
        const amount = e.target.querySelector('[name="amount"]')?.value;
        this.trackEvent('donation_initiated', {
          donation_amount: parseFloat(amount) || 0,
          donation_type: e.target.querySelector('[name="frequency"]')?.value || 'one-time',
          page_location: window.location.pathname
        });
      }
    });

    // Track event registrations
    document.addEventListener('submit', (e) => {
      if (e.target.classList.contains('rsvp-form')) {
        const eventId = e.target.querySelector('[name="eventId"]')?.value;
        this.trackEvent('event_registration', {
          event_id: eventId,
          page_location: window.location.pathname
        });
      }
    });

    // Track resource downloads
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('resource-download-btn')) {
        const resourceId = e.target.getAttribute('data-resource-id');
        this.trackEvent('resource_download', {
          resource_id: resourceId,
          page_location: window.location.pathname
        });
      }
    });

    // Track search usage
    document.addEventListener('input', (e) => {
      if (e.target.id === 'search-input' || e.target.classList.contains('search-input')) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
          if (e.target.value.length > 2) {
            this.trackEvent('search_performed', {
              search_term: e.target.value,
              page_location: window.location.pathname
            });
          }
        }, 1000);
      }
    });

    // Track social media clicks
    document.addEventListener('click', (e) => {
      const socialLink = e.target.closest('a[href*="facebook"], a[href*="instagram"], a[href*="linkedin"], a[href*="twitter"]');
      if (socialLink) {
        const platform = this.getSocialPlatform(socialLink.href);
        this.trackEvent('social_media_click', {
          platform: platform,
          page_location: window.location.pathname
        });
      }
    });

    // Track newsletter signups
    document.addEventListener('submit', (e) => {
      if (e.target.classList.contains('newsletter-form')) {
        this.trackEvent('newsletter_signup', {
          page_location: window.location.pathname
        });
      }
    });

    // Track contact form submissions
    document.addEventListener('submit', (e) => {
      if (e.target.classList.contains('contact-form')) {
        this.trackEvent('contact_form_submission', {
          page_location: window.location.pathname
        });
      }
    });
  }

  initializePerformanceMonitoring() {
    // Monitor Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordPerformanceMetric('LCP', lastEntry.startTime);

        // Send to Google Analytics
        gtag('event', 'web_vitals', {
          name: 'LCP',
          value: Math.round(lastEntry.startTime),
          event_category: 'Web Vitals'
        });
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          const fid = entry.processingStart - entry.startTime;
          this.recordPerformanceMetric('FID', fid);

          gtag('event', 'web_vitals', {
            name: 'FID',
            value: Math.round(fid),
            event_category: 'Web Vitals'
          });
        });
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.recordPerformanceMetric('CLS', clsValue);

        gtag('event', 'web_vitals', {
          name: 'CLS',
          value: Math.round(clsValue * 1000),
          event_category: 'Web Vitals'
        });
      }).observe({ entryTypes: ['layout-shift'] });
    }

    // Monitor page load times
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        const loadTime = navigation.loadEventEnd - navigation.fetchStart;
        this.recordPerformanceMetric('Page Load Time', loadTime);

        gtag('event', 'timing_complete', {
          name: 'page_load',
          value: Math.round(loadTime)
        });
      }, 0);
    });
  }

  setupUserBehaviorTracking() {
    // Track scroll depth
    let maxScrollDepth = 0;
    window.addEventListener('scroll', () => {
      const scrollDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
      if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;

        // Track milestone scroll depths
        if ([25, 50, 75, 90].includes(scrollDepth)) {
          this.trackEvent('scroll_depth', {
            scroll_depth: scrollDepth,
            page_location: window.location.pathname
          });
        }
      }
    });

    // Track time on page
    this.pageStartTime = Date.now();
    window.addEventListener('beforeunload', () => {
      const timeOnPage = Date.now() - this.pageStartTime;
      this.trackEvent('page_engagement', {
        time_on_page: Math.round(timeOnPage / 1000),
        page_location: window.location.pathname
      });
    });

    // Track click heatmap data
    if (this.config.enableHeatmaps) {
      document.addEventListener('click', (e) => {
        const rect = e.target.getBoundingClientRect();
        const clickData = {
          x: e.clientX,
          y: e.clientY,
          element: e.target.tagName,
          className: e.target.className,
          page: window.location.pathname,
          timestamp: Date.now()
        };
        this.recordClickData(clickData);
      });
    }

    // Track form interactions
    document.addEventListener('focus', (e) => {
      if (e.target.matches('input, textarea, select')) {
        this.trackEvent('form_field_focus', {
          field_name: e.target.name || e.target.id,
          field_type: e.target.type,
          page_location: window.location.pathname
        });
      }
    });
  }

  initializeSEOMonitoring() {
    if (!this.config.enableSEOMonitoring) return;

    // Monitor page SEO metrics
    const seoMetrics = this.analyzeSEOMetrics();
    this.analytics.seoMetrics.set(window.location.pathname, seoMetrics);

    // Track search engine referrals
    const referrer = document.referrer;
    if (referrer && this.isSearchEngine(referrer)) {
      this.trackEvent('organic_search_visit', {
        search_engine: this.getSearchEngine(referrer),
        page_location: window.location.pathname
      });
    }

    // Monitor page speed for SEO
    this.monitorPageSpeedForSEO();
  }

  createAnalyticsDashboard() {
    // Create analytics dashboard for administrators
    if (this.isAdminUser()) {
      this.createAdminDashboard();
    }
  }

  createAdminDashboard() {
    // Create floating analytics button
    const dashboardButton = document.createElement('button');
    dashboardButton.id = 'analytics-dashboard-btn';
    dashboardButton.className = 'fixed bottom-4 right-4 z-50 bg-gradient-to-r from-pink-600 to-purple-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105';
    dashboardButton.innerHTML = `
      <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 00-2 2"></path>
      </svg>
    `;
    dashboardButton.title = 'Analytics Dashboard';

    dashboardButton.addEventListener('click', () => {
      this.showAnalyticsDashboard();
    });

    document.body.appendChild(dashboardButton);
  }

  showAnalyticsDashboard() {
    const modal = this.createDashboardModal();
    document.body.appendChild(modal);

    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
      this.loadDashboardData();
    });
  }

  createDashboardModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden overflow-y-auto';
    modal.id = 'analytics-dashboard-modal';

    modal.innerHTML = `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-6xl w-full max-h-full overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-start mb-6">
            <div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Sisters for Good Analytics Dashboard</h2>
              <p class="text-gray-600 dark:text-gray-300">Real-time website analytics and performance monitoring</p>
            </div>
            <button onclick="this.closest('#analytics-dashboard-modal').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Dashboard Navigation -->
          <div class="flex space-x-4 mb-6 border-b border-gray-200 dark:border-gray-600">
            <button class="dashboard-tab active px-4 py-2 text-pink-600 border-b-2 border-pink-600 font-medium" data-tab="overview">
              Overview
            </button>
            <button class="dashboard-tab px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-pink-600 font-medium" data-tab="performance">
              Performance
            </button>
            <button class="dashboard-tab px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-pink-600 font-medium" data-tab="user-behavior">
              User Behavior
            </button>
            <button class="dashboard-tab px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-pink-600 font-medium" data-tab="seo">
              SEO
            </button>
            <button class="dashboard-tab px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-pink-600 font-medium" data-tab="conversions">
              Conversions
            </button>
          </div>

          <!-- Dashboard Content -->
          <div id="dashboard-content">
            <!-- Overview Tab -->
            <div id="overview-tab" class="dashboard-tab-content">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-blue-100">Total Page Views</p>
                      <p id="total-page-views" class="text-3xl font-bold">-</p>
                    </div>
                    <svg class="h-8 w-8 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  </div>
                </div>

                <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-green-100">Total Events</p>
                      <p id="total-events" class="text-3xl font-bold">-</p>
                    </div>
                    <svg class="h-8 w-8 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                  </div>
                </div>

                <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-purple-100">Avg. Performance Score</p>
                      <p id="avg-performance-score" class="text-3xl font-bold">-</p>
                    </div>
                    <svg class="h-8 w-8 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 00-2 2"></path>
                    </svg>
                  </div>
                </div>

                <div class="bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg p-6 text-white">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-pink-100">Conversion Rate</p>
                      <p id="conversion-rate" class="text-3xl font-bold">-</p>
                    </div>
                    <svg class="h-8 w-8 text-pink-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Recent Activity -->
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Recent Events</h3>
                  <div id="recent-events" class="space-y-3">
                    <!-- Recent events will be loaded here -->
                  </div>
                </div>

                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Top Pages</h3>
                  <div id="top-pages" class="space-y-3">
                    <!-- Top pages will be loaded here -->
                  </div>
                </div>
              </div>
            </div>

            <!-- Performance Tab -->
            <div id="performance-tab" class="dashboard-tab-content hidden">
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Core Web Vitals</h3>
                  <div id="core-web-vitals" class="space-y-4">
                    <!-- Core Web Vitals metrics -->
                  </div>
                </div>

                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Page Load Times</h3>
                  <div id="page-load-times" class="space-y-4">
                    <!-- Page load time metrics -->
                  </div>
                </div>

                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Resource Usage</h3>
                  <div id="resource-usage" class="space-y-4">
                    <!-- Resource usage metrics -->
                  </div>
                </div>
              </div>
            </div>

            <!-- User Behavior Tab -->
            <div id="user-behavior-tab" class="dashboard-tab-content hidden">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">User Engagement</h3>
                  <div id="user-engagement" class="space-y-4">
                    <!-- User engagement metrics -->
                  </div>
                </div>

                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Popular Content</h3>
                  <div id="popular-content" class="space-y-4">
                    <!-- Popular content metrics -->
                  </div>
                </div>
              </div>
            </div>

            <!-- SEO Tab -->
            <div id="seo-tab" class="dashboard-tab-content hidden">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">SEO Health</h3>
                  <div id="seo-health" class="space-y-4">
                    <!-- SEO health metrics -->
                  </div>
                </div>

                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Search Traffic</h3>
                  <div id="search-traffic" class="space-y-4">
                    <!-- Search traffic metrics -->
                  </div>
                </div>
              </div>
            </div>

            <!-- Conversions Tab -->
            <div id="conversions-tab" class="dashboard-tab-content hidden">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Donation Funnel</h3>
                  <div id="donation-funnel" class="space-y-4">
                    <!-- Donation funnel metrics -->
                  </div>
                </div>

                <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-6">
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Event Registrations</h3>
                  <div id="event-registrations" class="space-y-4">
                    <!-- Event registration metrics -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Dashboard Actions -->
          <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Last updated: <span id="last-updated">-</span>
            </div>
            <div class="flex gap-3">
              <button onclick="window.analyticsSystem.exportAnalyticsData()" class="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                Export Data
              </button>
              <button onclick="window.analyticsSystem.loadDashboardData()" class="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Setup tab switching
    const tabs = modal.querySelectorAll('.dashboard-tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs
        tabs.forEach(t => {
          t.classList.remove('active', 'text-pink-600', 'border-b-2', 'border-pink-600');
          t.classList.add('text-gray-600', 'dark:text-gray-300');
        });

        // Add active class to clicked tab
        tab.classList.add('active', 'text-pink-600', 'border-b-2', 'border-pink-600');
        tab.classList.remove('text-gray-600', 'dark:text-gray-300');

        // Hide all tab contents
        modal.querySelectorAll('.dashboard-tab-content').forEach(content => {
          content.classList.add('hidden');
        });

        // Show selected tab content
        const tabName = tab.getAttribute('data-tab');
        modal.querySelector(`#${tabName}-tab`).classList.remove('hidden');
      });
    });

    return modal;
  }

  loadDashboardData() {
    // Load overview data
    this.loadOverviewData();
    this.loadPerformanceData();
    this.loadUserBehaviorData();
    this.loadSEOData();
    this.loadConversionData();

    // Update last updated timestamp
    document.getElementById('last-updated').textContent = new Date().toLocaleString();
  }

  loadOverviewData() {
    // Total page views
    const totalPageViews = Array.from(this.analytics.pageViews.values()).reduce((sum, views) => sum + views, 0);
    document.getElementById('total-page-views').textContent = totalPageViews.toLocaleString();

    // Total events
    document.getElementById('total-events').textContent = this.analytics.events.length.toLocaleString();

    // Average performance score
    const avgScore = this.calculatePerformanceScore();
    document.getElementById('avg-performance-score').textContent = avgScore + '%';

    // Conversion rate (placeholder)
    document.getElementById('conversion-rate').textContent = '3.2%';

    // Recent events
    const recentEvents = this.analytics.events.slice(-5).reverse();
    const recentEventsContainer = document.getElementById('recent-events');
    recentEventsContainer.innerHTML = recentEvents.map(event => `
      <div class="flex items-center justify-between p-3 bg-white dark:bg-slate-600 rounded-lg">
        <div>
          <p class="font-medium text-gray-900 dark:text-white">${event.name}</p>
          <p class="text-sm text-gray-600 dark:text-gray-300">${event.page}</p>
        </div>
        <span class="text-xs text-gray-500 dark:text-gray-400">
          ${new Date(event.timestamp).toLocaleTimeString()}
        </span>
      </div>
    `).join('');

    // Top pages
    const topPages = Array.from(this.analytics.pageViews.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);

    const topPagesContainer = document.getElementById('top-pages');
    topPagesContainer.innerHTML = topPages.map(([page, views]) => `
      <div class="flex items-center justify-between p-3 bg-white dark:bg-slate-600 rounded-lg">
        <div>
          <p class="font-medium text-gray-900 dark:text-white">${page}</p>
        </div>
        <span class="text-sm font-bold text-pink-600">${views} views</span>
      </div>
    `).join('');
  }

  loadPerformanceData() {
    // Core Web Vitals
    const coreWebVitalsContainer = document.getElementById('core-web-vitals');
    if (coreWebVitalsContainer) {
      const performanceData = JSON.parse(localStorage.getItem('sfg-performance-metrics') || '[]');
      const latestMetrics = performanceData.length > 0 ? performanceData[performanceData.length - 1][1] : {};

      coreWebVitalsContainer.innerHTML = `
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">LCP</span>
            <span class="font-bold ${(latestMetrics.LCP || 0) < 2500 ? 'text-green-600' : 'text-red-600'}">
              ${latestMetrics.LCP ? Math.round(latestMetrics.LCP) + 'ms' : 'N/A'}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">FID</span>
            <span class="font-bold ${(latestMetrics.FID || 0) < 100 ? 'text-green-600' : 'text-red-600'}">
              ${latestMetrics.FID ? Math.round(latestMetrics.FID) + 'ms' : 'N/A'}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">CLS</span>
            <span class="font-bold ${(latestMetrics.CLS || 0) < 0.1 ? 'text-green-600' : 'text-red-600'}">
              ${latestMetrics.CLS ? latestMetrics.CLS.toFixed(3) : 'N/A'}
            </span>
          </div>
        </div>
      `;
    }
  }

  loadUserBehaviorData() {
    // User engagement metrics
    const userEngagementContainer = document.getElementById('user-engagement');
    if (userEngagementContainer) {
      const engagementEvents = this.analytics.events.filter(e => e.name === 'page_engagement');
      const avgTimeOnPage = engagementEvents.length > 0
        ? engagementEvents.reduce((sum, e) => sum + (e.parameters.time_on_page || 0), 0) / engagementEvents.length
        : 0;

      userEngagementContainer.innerHTML = `
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">Avg. Time on Page</span>
            <span class="font-bold text-blue-600">${Math.round(avgTimeOnPage)}s</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">Bounce Rate</span>
            <span class="font-bold text-orange-600">32%</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">Pages per Session</span>
            <span class="font-bold text-green-600">2.4</span>
          </div>
        </div>
      `;
    }
  }

  loadSEOData() {
    // SEO health metrics
    const seoHealthContainer = document.getElementById('seo-health');
    if (seoHealthContainer) {
      const seoScore = this.calculateAccessibilityScore();

      seoHealthContainer.innerHTML = `
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">SEO Score</span>
            <span class="font-bold ${seoScore > 80 ? 'text-green-600' : seoScore > 60 ? 'text-orange-600' : 'text-red-600'}">
              ${seoScore}%
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">Meta Descriptions</span>
            <span class="font-bold text-green-600">✓ Complete</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">Image Alt Tags</span>
            <span class="font-bold text-green-600">✓ Complete</span>
          </div>
        </div>
      `;
    }
  }

  loadConversionData() {
    // Donation funnel
    const donationFunnelContainer = document.getElementById('donation-funnel');
    if (donationFunnelContainer) {
      const donationEvents = this.analytics.events.filter(e => e.name === 'donation_initiated');

      donationFunnelContainer.innerHTML = `
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">Donation Page Views</span>
            <span class="font-bold text-blue-600">${this.analytics.pageViews.get('/donate.html') || 0}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">Donation Attempts</span>
            <span class="font-bold text-orange-600">${donationEvents.length}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-700 dark:text-gray-300">Conversion Rate</span>
            <span class="font-bold text-green-600">
              ${this.analytics.pageViews.get('/donate.html') ?
                Math.round((donationEvents.length / this.analytics.pageViews.get('/donate.html')) * 100) : 0}%
            </span>
          </div>
        </div>
      `;
    }
  }

  startRealTimeMonitoring() {
    if (!this.config.enableRealTimeMonitoring) return;

    // Monitor website health
    setInterval(() => {
      this.checkWebsiteHealth();
    }, 60000); // Check every minute

    // Monitor performance metrics
    setInterval(() => {
      this.collectPerformanceSnapshot();
    }, 30000); // Collect every 30 seconds
  }

  // Event tracking methods
  trackEvent(eventName, parameters = {}) {
    // Store locally
    this.analytics.events.push({
      name: eventName,
      parameters: parameters,
      timestamp: Date.now(),
      page: window.location.pathname,
      userAgent: navigator.userAgent
    });

    // Send to Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        ...parameters,
        event_category: 'Sisters for Good',
        event_label: window.location.pathname
      });
    }

    // Store in localStorage for offline tracking
    this.storeOfflineEvent(eventName, parameters);
  }

  trackPageView(page = window.location.pathname) {
    const pageViewData = {
      page: page,
      title: document.title,
      timestamp: Date.now(),
      referrer: document.referrer,
      userAgent: navigator.userAgent
    };

    // Store locally
    const currentViews = this.analytics.pageViews.get(page) || 0;
    this.analytics.pageViews.set(page, currentViews + 1);

    // Send to Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('config', this.config.googleAnalyticsId, {
        page_title: document.title,
        page_location: window.location.href
      });
    }

    // Track conversion funnel
    this.trackConversionFunnel(page);
  }

  trackConversionFunnel(page) {
    const funnelSteps = {
      '/': 'homepage_visit',
      '/about.html': 'about_page_visit',
      '/programs.html': 'programs_page_visit',
      '/events.html': 'events_page_visit',
      '/donate.html': 'donation_page_visit',
      '/contact.html': 'contact_page_visit'
    };

    if (funnelSteps[page]) {
      this.trackEvent('funnel_step', {
        step_name: funnelSteps[page],
        step_number: Object.keys(funnelSteps).indexOf(page) + 1
      });
    }
  }

  // Performance monitoring methods
  recordPerformanceMetric(name, value) {
    const metrics = this.analytics.performanceMetrics.get(window.location.pathname) || {};
    metrics[name] = value;
    metrics.timestamp = Date.now();
    this.analytics.performanceMetrics.set(window.location.pathname, metrics);

    // Store in localStorage
    localStorage.setItem('sfg-performance-metrics', JSON.stringify(Array.from(this.analytics.performanceMetrics.entries())));
  }

  collectPerformanceSnapshot() {
    const snapshot = {
      timestamp: Date.now(),
      page: window.location.pathname,
      memory: performance.memory ? {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize
      } : null,
      navigation: performance.getEntriesByType('navigation')[0],
      resources: performance.getEntriesByType('resource').length
    };

    // Store snapshot
    const snapshots = JSON.parse(localStorage.getItem('sfg-performance-snapshots') || '[]');
    snapshots.push(snapshot);

    // Keep only last 100 snapshots
    if (snapshots.length > 100) {
      snapshots.shift();
    }

    localStorage.setItem('sfg-performance-snapshots', JSON.stringify(snapshots));
  }

  // SEO monitoring methods
  analyzeSEOMetrics() {
    const metrics = {
      title: document.title,
      titleLength: document.title.length,
      metaDescription: document.querySelector('meta[name="description"]')?.content || '',
      metaDescriptionLength: (document.querySelector('meta[name="description"]')?.content || '').length,
      h1Count: document.querySelectorAll('h1').length,
      h2Count: document.querySelectorAll('h2').length,
      imageCount: document.querySelectorAll('img').length,
      imagesWithoutAlt: document.querySelectorAll('img:not([alt])').length,
      internalLinks: document.querySelectorAll('a[href^="/"], a[href^="./"], a[href^="../"]').length,
      externalLinks: document.querySelectorAll('a[href^="http"]:not([href*="sistersforgood.org"])').length,
      pageSize: document.documentElement.outerHTML.length,
      timestamp: Date.now()
    };

    return metrics;
  }

  monitorPageSpeedForSEO() {
    // Monitor metrics important for SEO
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (entry.entryType === 'navigation') {
          const seoSpeedMetrics = {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            firstByte: entry.responseStart - entry.requestStart,
            timestamp: Date.now()
          };

          // Store SEO speed metrics
          localStorage.setItem('sfg-seo-speed-metrics', JSON.stringify(seoSpeedMetrics));
        }
      });
    });

    observer.observe({ entryTypes: ['navigation'] });
  }

  // Website health monitoring
  checkWebsiteHealth() {
    const healthCheck = {
      timestamp: Date.now(),
      page: window.location.pathname,
      online: navigator.onLine,
      jsErrors: this.getJSErrorCount(),
      performanceScore: this.calculatePerformanceScore(),
      accessibilityScore: this.calculateAccessibilityScore()
    };

    // Store health check
    const healthChecks = JSON.parse(localStorage.getItem('sfg-health-checks') || '[]');
    healthChecks.push(healthCheck);

    // Keep only last 50 health checks
    if (healthChecks.length > 50) {
      healthChecks.shift();
    }

    localStorage.setItem('sfg-health-checks', JSON.stringify(healthChecks));

    // Alert if critical issues detected
    if (!healthCheck.online || healthCheck.performanceScore < 50) {
      this.alertCriticalIssue(healthCheck);
    }
  }

  // Utility methods
  getSocialPlatform(url) {
    if (url.includes('facebook')) return 'facebook';
    if (url.includes('instagram')) return 'instagram';
    if (url.includes('linkedin')) return 'linkedin';
    if (url.includes('twitter')) return 'twitter';
    return 'unknown';
  }

  isSearchEngine(referrer) {
    const searchEngines = ['google', 'bing', 'yahoo', 'duckduckgo', 'baidu'];
    return searchEngines.some(engine => referrer.includes(engine));
  }

  getSearchEngine(referrer) {
    if (referrer.includes('google')) return 'google';
    if (referrer.includes('bing')) return 'bing';
    if (referrer.includes('yahoo')) return 'yahoo';
    if (referrer.includes('duckduckgo')) return 'duckduckgo';
    return 'other';
  }

  isAdminUser() {
    // Use global admin check function if available
    if (typeof window.isAdminUser === 'function') {
      return window.isAdminUser();
    }

    // Fallback to existing methods
    return localStorage.getItem('sfg-admin-access') === 'true' ||
           sessionStorage.getItem('sfg-admin-session') === 'true' ||
           window.location.search.includes('admin=true');
  }

  recordClickData(clickData) {
    const clickHeatmap = JSON.parse(localStorage.getItem('sfg-click-heatmap') || '[]');
    clickHeatmap.push(clickData);

    // Keep only last 1000 clicks
    if (clickHeatmap.length > 1000) {
      clickHeatmap.shift();
    }

    localStorage.setItem('sfg-click-heatmap', JSON.stringify(clickHeatmap));
  }

  storeOfflineEvent(eventName, parameters) {
    const offlineEvents = JSON.parse(localStorage.getItem('sfg-offline-events') || '[]');
    offlineEvents.push({
      name: eventName,
      parameters: parameters,
      timestamp: Date.now()
    });

    localStorage.setItem('sfg-offline-events', JSON.stringify(offlineEvents));
  }

  getJSErrorCount() {
    return parseInt(localStorage.getItem('sfg-js-error-count') || '0');
  }

  calculatePerformanceScore() {
    const metrics = this.analytics.performanceMetrics.get(window.location.pathname);
    if (!metrics) return 100;

    let score = 100;
    if (metrics.LCP > 2500) score -= 20;
    if (metrics.FID > 100) score -= 20;
    if (metrics.CLS > 0.1) score -= 20;
    if (metrics['Page Load Time'] > 3000) score -= 20;

    return Math.max(0, score);
  }

  calculateAccessibilityScore() {
    let score = 100;

    // Check for common accessibility issues
    if (document.querySelectorAll('img:not([alt])').length > 0) score -= 10;
    if (document.querySelectorAll('a:not([aria-label]):not([title])').length > 0) score -= 10;
    if (document.querySelectorAll('button:not([aria-label]):not([title])').length > 0) score -= 10;
    if (!document.querySelector('h1')) score -= 20;

    return Math.max(0, score);
  }

  alertCriticalIssue(healthCheck) {
    console.warn('Critical website issue detected:', healthCheck);

    // In production, this would send alerts to administrators
    if (this.isAdminUser()) {
      this.showAdminAlert('Critical website issue detected. Check the analytics dashboard for details.');
    }
  }

  showAdminAlert(message) {
    const alert = document.createElement('div');
    alert.className = 'fixed top-4 right-4 z-50 bg-red-600 text-white p-4 rounded-lg shadow-lg max-w-sm';
    alert.innerHTML = `
      <div class="flex items-center">
        <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span class="font-medium">Admin Alert</span>
      </div>
      <p class="mt-2 text-sm">${message}</p>
      <button onclick="this.parentElement.remove()" class="mt-2 text-xs underline">Dismiss</button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
      alert.remove();
    }, 10000);
  }

  // Public API methods
  getAnalyticsData() {
    return {
      pageViews: Object.fromEntries(this.analytics.pageViews),
      events: this.analytics.events,
      performanceMetrics: Object.fromEntries(this.analytics.performanceMetrics),
      seoMetrics: Object.fromEntries(this.analytics.seoMetrics)
    };
  }

  exportAnalyticsData() {
    const data = {
      ...this.getAnalyticsData(),
      exportDate: new Date().toISOString(),
      website: 'Sisters for Good'
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sisters-for-good-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

// Error tracking
window.addEventListener('error', (e) => {
  const errorCount = parseInt(localStorage.getItem('sfg-js-error-count') || '0') + 1;
  localStorage.setItem('sfg-js-error-count', errorCount.toString());

  // Track error in analytics
  if (window.analyticsSystem) {
    window.analyticsSystem.trackEvent('javascript_error', {
      error_message: e.message,
      error_filename: e.filename,
      error_line: e.lineno,
      page_location: window.location.pathname
    });
  }
});

// Initialize analytics system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.analyticsSystem = new AnalyticsMonitoringSystem();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AnalyticsMonitoringSystem;
}
