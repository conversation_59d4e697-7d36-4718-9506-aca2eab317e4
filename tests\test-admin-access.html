<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Access Test - Sisters for Good</title>
  <link rel="stylesheet" href="css/main.css">
  <style>
    .test-container {
      max-width: 800px;
      margin: 2rem auto;
      padding: 2rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .test-section {
      margin: 2rem 0;
      padding: 1.5rem;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
    }
    .password-display {
      font-family: 'Courier New', monospace;
      background: #f3f4f6;
      padding: 0.5rem;
      border-radius: 4px;
      font-size: 1.2rem;
      letter-spacing: 2px;
      text-align: center;
      margin: 1rem 0;
    }
    .status-indicator {
      padding: 0.5rem 1rem;
      border-radius: 4px;
      font-weight: bold;
      text-align: center;
      margin: 1rem 0;
    }
    .status-inactive {
      background: #fee2e2;
      color: #dc2626;
    }
    .status-active {
      background: #dcfce7;
      color: #16a34a;
    }
    .test-input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      margin: 0.5rem 0;
    }
    .test-button {
      background: #3b82f6;
      color: white;
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 0.25rem;
    }
    .test-button:hover {
      background: #2563eb;
    }
    .admin-features-list {
      background: #f8fafc;
      padding: 1rem;
      border-radius: 4px;
      margin: 1rem 0;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">🔐 Secure Admin Access Test</h1>
    
    <div class="test-section">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Admin Password</h2>
      <p class="text-gray-600 mb-4">
        The admin password is: <strong>9733459376</strong>
      </p>
      <div class="password-display">9733459376</div>
      <p class="text-sm text-gray-500">
        Type this sequence anywhere on the contact page to activate admin mode.
      </p>
    </div>

    <div class="test-section">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Current Admin Status</h2>
      <div id="admin-status" class="status-indicator status-inactive">
        Admin Mode: INACTIVE
      </div>
      <button onclick="checkAdminStatus()" class="test-button">Refresh Status</button>
      <button onclick="testManualActivation()" class="test-button">Manual Activation (Test)</button>
      <button onclick="clearAdminAccess()" class="test-button">Clear Admin Access</button>
    </div>

    <div class="test-section">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Testing Instructions</h2>
      <ol class="list-decimal list-inside space-y-2 text-gray-700">
        <li><strong>Navigate to contact.html</strong> - The secure admin access only works on the contact page</li>
        <li><strong>Type the password anywhere</strong> - You can type "9733459376" while:
          <ul class="list-disc list-inside ml-6 mt-2 space-y-1">
            <li>Filling out the contact form</li>
            <li>Scrolling through the page</li>
            <li>Clicking on different elements</li>
            <li>Having focus on any element</li>
          </ul>
        </li>
        <li><strong>Watch for confirmation</strong> - A green notification should appear saying "Admin Access Activated"</li>
        <li><strong>Verify admin features</strong> - You should see:
          <ul class="list-disc list-inside ml-6 mt-2 space-y-1">
            <li>Analytics dashboard button (bottom-right corner)</li>
            <li>Admin mode indicator (top-left corner)</li>
            <li>SEO recommendations panel (if applicable)</li>
          </ul>
        </li>
      </ol>
    </div>

    <div class="test-section">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Test Form (Simulate Contact Form)</h2>
      <p class="text-gray-600 mb-4">
        Try typing the admin password while filling out this form to test the functionality:
      </p>
      <form class="space-y-4">
        <input type="text" placeholder="Your Name" class="test-input">
        <input type="email" placeholder="Your Email" class="test-input">
        <textarea placeholder="Your Message" rows="4" class="test-input"></textarea>
        <button type="button" class="test-button">Submit (Test Button)</button>
      </form>
      <p class="text-sm text-gray-500 mt-2">
        <strong>Test:</strong> While typing in any of these fields, include the password "9733459376" in your text.
      </p>
    </div>

    <div class="test-section">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Expected Admin Features</h2>
      <div class="admin-features-list">
        <h3 class="font-bold text-gray-800 mb-2">When admin mode is activated, you should see:</h3>
        <ul class="list-disc list-inside space-y-1 text-gray-700">
          <li>🎯 <strong>Analytics Dashboard Button</strong> - Floating button in bottom-right corner</li>
          <li>🔧 <strong>Admin Mode Indicator</strong> - "ADMIN" badge in top-left corner</li>
          <li>📊 <strong>Analytics Dashboard</strong> - Accessible by clicking the dashboard button</li>
          <li>🔍 <strong>SEO Recommendations</strong> - Automatic SEO analysis panel</li>
          <li>⚡ <strong>Performance Monitoring</strong> - Enhanced performance tracking</li>
          <li>✅ <strong>Confirmation Notification</strong> - Green success message</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Security Features</h2>
      <ul class="list-disc list-inside space-y-2 text-gray-700">
        <li><strong>Invisible Password Entry</strong> - No visual indication while typing</li>
        <li><strong>Global Key Capture</strong> - Works regardless of current focus</li>
        <li><strong>Session Persistence</strong> - Admin mode persists for the current session</li>
        <li><strong>Page-Specific</strong> - Only works on contact.html page</li>
        <li><strong>Secure Storage</strong> - Uses localStorage and sessionStorage</li>
        <li><strong>Easy Deactivation</strong> - Click admin indicator to deactivate</li>
      </ul>
    </div>

    <div class="test-section">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Navigation Links</h2>
      <div class="space-x-4">
        <a href="contact.html" class="test-button">Go to Contact Page (Test Here)</a>
        <a href="index.html" class="test-button">Home Page</a>
        <a href="about.html" class="test-button">About Page</a>
      </div>
      <p class="text-sm text-gray-500 mt-2">
        Remember: The secure admin access only works on the contact.html page.
      </p>
    </div>
  </div>

  <!-- Scripts -->
  <script src="js/secure-admin-access.js"></script>
  
  <script>
    function checkAdminStatus() {
      const isAdmin = typeof window.isAdminUser === 'function' ? window.isAdminUser() : false;
      const statusElement = document.getElementById('admin-status');
      
      if (isAdmin) {
        statusElement.textContent = 'Admin Mode: ACTIVE ✅';
        statusElement.className = 'status-indicator status-active';
      } else {
        statusElement.textContent = 'Admin Mode: INACTIVE ❌';
        statusElement.className = 'status-indicator status-inactive';
      }
      
      console.log('Admin status check:', isAdmin);
    }
    
    function testManualActivation() {
      if (window.secureAdminAccess && typeof window.secureAdminAccess.manualActivation === 'function') {
        window.secureAdminAccess.manualActivation();
        setTimeout(checkAdminStatus, 500);
      } else {
        alert('Secure admin access system not available. Make sure you\'re on the contact page.');
      }
    }
    
    function clearAdminAccess() {
      if (window.secureAdminAccess && typeof window.secureAdminAccess.clearAdminAccess === 'function') {
        window.secureAdminAccess.clearAdminAccess();
      }
      
      // Also clear storage manually
      localStorage.removeItem('sfg-admin-access');
      sessionStorage.removeItem('sfg-admin-session');
      
      setTimeout(checkAdminStatus, 500);
      alert('Admin access cleared. Refresh the page to see changes.');
    }
    
    // Check status on page load
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(checkAdminStatus, 1000);
      
      // Add keypress logging for testing
      document.addEventListener('keydown', (e) => {
        if (e.key && /[0-9]/.test(e.key)) {
          console.log('Number key pressed:', e.key);
        }
      });
    });
    
    // Monitor admin status changes
    setInterval(checkAdminStatus, 2000);
  </script>
</body>
</html>
