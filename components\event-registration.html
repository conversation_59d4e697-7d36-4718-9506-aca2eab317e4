<!-- Event Registration Modal -->
<div id="event-registration-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
    <!-- Modal Header -->
    <div class="bg-primary text-white p-4">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-bold" id="modal-event-title">Event Registration</h3>
        <button id="close-modal" class="text-white hover:text-gray-200 focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Modal Body -->
    <div class="p-6">
      <form id="event-registration-form">
        <input type="hidden" id="event-id" name="eventId">
        
        <div class="mb-4">
          <label for="name" class="block text-gray-700 font-bold mb-2">Full Name</label>
          <input type="text" id="name" name="name" required
                 class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
        </div>
        
        <div class="mb-4">
          <label for="email" class="block text-gray-700 font-bold mb-2">Email Address</label>
          <input type="email" id="email" name="email" required
                 class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
        </div>
        
        <div class="mb-4">
          <label for="phone" class="block text-gray-700 font-bold mb-2">Phone Number</label>
          <input type="tel" id="phone" name="phone" 
                 class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
        </div>
        
        <div class="mb-4">
          <label for="guests" class="block text-gray-700 font-bold mb-2">Number of Guests</label>
          <select id="guests" name="guests" 
                  class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
            <option value="1">Just me</option>
            <option value="2">2 people</option>
            <option value="3">3 people</option>
            <option value="4">4 people</option>
            <option value="5">5+ people</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label for="notes" class="block text-gray-700 font-bold mb-2">Special Notes or Requirements</label>
          <textarea id="notes" name="notes" rows="3"
                    class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
        </div>
        
        <div class="flex justify-end">
          <button type="button" id="cancel-registration" class="btn btn-secondary mr-2">Cancel</button>
          <button type="submit" class="btn btn-primary">Register</button>
        </div>
      </form>
    </div>
  </div>
</div>
