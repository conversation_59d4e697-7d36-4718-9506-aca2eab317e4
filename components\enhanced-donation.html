<!-- Enhanced Donation Component with Advanced Features -->
<div id="enhanced-donation-container" class="space-y-8">
  <!-- Donation Impact Hero -->
  <div class="bg-gradient-to-r from-pink-600 to-purple-600 text-white rounded-lg p-8 text-center">
    <h2 class="text-3xl font-bold mb-4">Make a Lasting Impact</h2>
    <p class="text-xl mb-6">Your donation directly supports women's empowerment and community strengthening initiatives</p>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
      <div class="bg-white/10 rounded-lg p-4">
        <div class="text-3xl mb-2">💇‍♀️</div>
        <div class="text-2xl font-bold">$500</div>
        <div class="text-sm">Supports one cosmetology student for a month</div>
      </div>
      <div class="bg-white/10 rounded-lg p-4">
        <div class="text-3xl mb-2">🎒</div>
        <div class="text-2xl font-bold">$50</div>
        <div class="text-sm">Complete back-to-school kit for one child</div>
      </div>
      <div class="bg-white/10 rounded-lg p-4">
        <div class="text-3xl mb-2">🤝</div>
        <div class="text-2xl font-bold">$200</div>
        <div class="text-sm">Host one community gathering</div>
      </div>
    </div>
  </div>

  <!-- Quick Donation Buttons -->
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 transition-colors duration-300">
    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Quick Donation</h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
      <button class="quick-donate-btn bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 py-3 px-4 rounded-lg hover:bg-pink-200 dark:hover:bg-pink-900/50 transition-colors font-semibold" data-amount="25">
        $25
      </button>
      <button class="quick-donate-btn bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 py-3 px-4 rounded-lg hover:bg-pink-200 dark:hover:bg-pink-900/50 transition-colors font-semibold" data-amount="50">
        $50
      </button>
      <button class="quick-donate-btn bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 py-3 px-4 rounded-lg hover:bg-pink-200 dark:hover:bg-pink-900/50 transition-colors font-semibold" data-amount="100">
        $100
      </button>
      <button class="quick-donate-btn bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300 py-3 px-4 rounded-lg hover:bg-pink-200 dark:hover:bg-pink-900/50 transition-colors font-semibold" data-amount="250">
        $250
      </button>
    </div>
    <div class="flex gap-3">
      <button class="impact-calculator-btn flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
        💡 Impact Calculator
      </button>
      <button class="donor-wall-btn flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
        🏆 Donor Wall
      </button>
    </div>
  </div>

  <!-- Main Donation Form -->
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 transition-colors duration-300">
    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Complete Your Donation</h3>
    
    <form class="donation-form space-y-6">
      <!-- Donation Amount -->
      <div>
        <label for="donation-amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Donation Amount *
        </label>
        <div class="relative">
          <span class="absolute left-3 top-3 text-gray-500 text-lg">$</span>
          <input type="number" id="donation-amount" name="amount" min="1" step="1" required
                 class="w-full pl-8 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white text-lg"
                 placeholder="100">
        </div>
      </div>

      <!-- Recurring Donation -->
      <div>
        <div class="flex items-center mb-3">
          <input type="checkbox" id="recurring-donation" name="recurring" 
                 class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
          <label for="recurring-donation" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            Make this a recurring donation
          </label>
        </div>
        <div id="recurring-options" style="display: none;" class="ml-6 space-y-2">
          <label class="flex items-center">
            <input type="radio" name="frequency" value="monthly" class="h-4 w-4 text-pink-600 focus:ring-pink-500">
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Monthly</span>
          </label>
          <label class="flex items-center">
            <input type="radio" name="frequency" value="quarterly" class="h-4 w-4 text-pink-600 focus:ring-pink-500">
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Quarterly</span>
          </label>
          <label class="flex items-center">
            <input type="radio" name="frequency" value="annually" class="h-4 w-4 text-pink-600 focus:ring-pink-500">
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Annually</span>
          </label>
        </div>
      </div>

      <!-- Impact Display -->
      <div id="impact-display" class="bg-pink-50 dark:bg-pink-900/20 rounded-lg p-4">
        <p class="text-gray-500 dark:text-gray-400">Enter an amount to see your impact</p>
      </div>

      <!-- Donor Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="donorName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Full Name *
          </label>
          <input type="text" id="donorName" name="donorName" required
                 class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
        </div>
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Email Address *
          </label>
          <input type="email" id="email" name="email" required
                 class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Phone Number
          </label>
          <input type="tel" id="phone" name="phone"
                 class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
        </div>
        <div>
          <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Address
          </label>
          <input type="text" id="address" name="address"
                 class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            City
          </label>
          <input type="text" id="city" name="city"
                 class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
        </div>
        <div>
          <label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            State
          </label>
          <select id="state" name="state"
                  class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
            <option value="">Select State</option>
            <option value="PA">Pennsylvania</option>
            <option value="NJ">New Jersey</option>
            <option value="NY">New York</option>
            <option value="DE">Delaware</option>
            <!-- Add more states as needed -->
          </select>
        </div>
        <div>
          <label for="zipCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            ZIP Code
          </label>
          <input type="text" id="zipCode" name="zipCode"
                 class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
        </div>
      </div>

      <!-- Dedication Options -->
      <div>
        <div class="flex items-center mb-3">
          <input type="checkbox" id="dedication" name="dedication" 
                 class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
          <label for="dedication" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            Make this donation in honor/memory of someone
          </label>
        </div>
        <div id="dedication-options" style="display: none;" class="space-y-3">
          <input type="text" name="dedicationName" placeholder="Person's name"
                 class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
          <textarea name="dedicationMessage" rows="3" placeholder="Optional message..."
                    class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"></textarea>
        </div>
      </div>

      <!-- Privacy Options -->
      <div class="space-y-3">
        <div class="flex items-center">
          <input type="checkbox" id="anonymous" name="anonymous" 
                 class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
          <label for="anonymous" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Make this donation anonymous
          </label>
        </div>
        <div class="flex items-center">
          <input type="checkbox" id="newsletter" name="newsletter" checked
                 class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
          <label for="newsletter" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Subscribe to our newsletter for updates on our impact
          </label>
        </div>
      </div>

      <!-- Payment Method -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Payment Method *
        </label>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
          <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700">
            <input type="radio" name="paymentMethod" value="credit-card" checked class="h-4 w-4 text-pink-600 focus:ring-pink-500">
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">💳 Credit Card</span>
          </label>
          <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700">
            <input type="radio" name="paymentMethod" value="paypal" class="h-4 w-4 text-pink-600 focus:ring-pink-500">
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">🅿️ PayPal</span>
          </label>
          <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700">
            <input type="radio" name="paymentMethod" value="bank-transfer" class="h-4 w-4 text-pink-600 focus:ring-pink-500">
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">🏦 Bank Transfer</span>
          </label>
        </div>
      </div>

      <!-- Submit Button -->
      <div class="pt-4">
        <button type="submit" class="w-full bg-gradient-to-r from-pink-600 to-purple-600 text-white py-4 px-6 rounded-lg hover:from-pink-700 hover:to-purple-700 transition-all duration-300 font-bold text-lg transform hover:scale-105 shadow-lg">
          💝 Complete Donation
        </button>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
          Your donation is secure and tax-deductible. You'll receive a receipt via email.
        </p>
      </div>
    </form>
  </div>

  <!-- Donor Recognition -->
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 transition-colors duration-300">
    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Recent Supporters</h3>
    <div id="recent-donors" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- Recent donors will be dynamically loaded here -->
    </div>
    <div class="text-center mt-6">
      <button class="donor-wall-btn bg-purple-600 text-white py-2 px-6 rounded-lg hover:bg-purple-700 transition-colors">
        View All Supporters
      </button>
    </div>
  </div>

  <!-- Tax Information -->
  <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
    <h3 class="text-lg font-bold text-blue-900 dark:text-blue-300 mb-3">Tax Deductible Information</h3>
    <p class="text-blue-800 dark:text-blue-200 mb-3">
      Sisters for Good is a 501(c)(3) nonprofit organization. Your donation is tax-deductible to the full extent allowed by law.
    </p>
    <div class="text-sm text-blue-700 dark:text-blue-300">
      <p><strong>Tax ID:</strong> XX-XXXXXXX</p>
      <p><strong>Address:</strong> 247 East High Street, Pottstown, PA 19464</p>
      <p>You will receive a tax receipt via email after your donation is processed.</p>
    </div>
  </div>
</div>

<script>
// Enhanced Donation Component JavaScript
document.addEventListener('DOMContentLoaded', function() {
  // Setup dedication toggle
  const dedicationCheckbox = document.getElementById('dedication');
  const dedicationOptions = document.getElementById('dedication-options');
  
  if (dedicationCheckbox && dedicationOptions) {
    dedicationCheckbox.addEventListener('change', function() {
      dedicationOptions.style.display = this.checked ? 'block' : 'none';
    });
  }

  // Load recent donors
  loadRecentDonors();
});

function loadRecentDonors() {
  const recentDonorsContainer = document.getElementById('recent-donors');
  if (!recentDonorsContainer) return;

  // Sample recent donors (in real implementation, load from API)
  const recentDonors = [
    { name: 'Anonymous', amount: 100, tier: 'bronze', date: '2024-12-01' },
    { name: 'Sarah M.', amount: 250, tier: 'silver', date: '2024-11-28' },
    { name: 'Community Foundation', amount: 1000, tier: 'gold', date: '2024-11-25' },
    { name: 'John & Mary K.', amount: 500, tier: 'silver', date: '2024-11-22' },
    { name: 'Local Business Group', amount: 2500, tier: 'platinum', date: '2024-11-20' },
    { name: 'Anonymous', amount: 75, tier: 'bronze', date: '2024-11-18' }
  ];

  recentDonorsContainer.innerHTML = recentDonors.map(donor => `
    <div class="bg-gray-50 dark:bg-slate-700 rounded-lg p-4 text-center">
      <div class="text-2xl mb-2">${getTierIcon(donor.tier)}</div>
      <div class="font-semibold text-gray-900 dark:text-white">${donor.name}</div>
      <div class="text-sm text-gray-600 dark:text-gray-300">$${donor.amount}</div>
      <div class="text-xs text-gray-500 dark:text-gray-400">${new Date(donor.date).toLocaleDateString()}</div>
    </div>
  `).join('');
}

function getTierIcon(tier) {
  const icons = {
    bronze: '🥉',
    silver: '🥈', 
    gold: '🥇',
    platinum: '💎'
  };
  return icons[tier] || '💝';
}
</script>

<style>
/* Enhanced Donation Component Styles */
.donation-form input:focus,
.donation-form select:focus,
.donation-form textarea:focus {
  ring: 2px;
  ring-color: #fc5c7d;
  border-color: #fc5c7d;
}

.quick-donate-btn.active {
  background: linear-gradient(135deg, #fc5c7d, #6a82fb);
  color: white;
}

/* Donation amount input styling */
#donation-amount {
  font-size: 1.25rem;
  font-weight: 600;
}

/* Impact display animations */
#impact-display {
  transition: all 0.3s ease;
}

/* Donor tier styling */
.donor-tier-bronze { border-left: 4px solid #cd7f32; }
.donor-tier-silver { border-left: 4px solid #c0c0c0; }
.donor-tier-gold { border-left: 4px solid #ffd700; }
.donor-tier-platinum { border-left: 4px solid #e5e4e2; }

/* Loading animation for donation processing */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
