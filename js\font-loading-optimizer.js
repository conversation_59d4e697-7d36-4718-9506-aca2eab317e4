/**
 * Font Loading Optimizer
 * Prevents header size jumping and layout shifts during font loading
 */

class FontLoadingOptimizer {
  constructor() {
    this.fontsToLoad = [
      {
        family: 'Poppins',
        weights: ['400', '500', '600', '700'],
        display: 'swap'
      },
      {
        family: 'Roboto',
        weights: ['400', '500'],
        display: 'swap'
      }
    ];
    this.loadingClass = 'fonts-loading';
    this.loadedClass = 'fonts-loaded';
    this.init();
  }

  init() {
    // Only run if DOM is ready to prevent conflicts
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.startOptimization());
    } else {
      this.startOptimization();
    }
  }

  startOptimization() {
    // Add loading class with delay to prevent conflicts
    requestAnimationFrame(() => {
      document.documentElement.classList.add(this.loadingClass);

      // Prevent layout shifts during loading
      this.preventLayoutShifts();

      // Use optimized font loading approach
      this.optimizedFontLoading();
    });
  }

  optimizedFontLoading() {
    // Use CSS-based font loading instead of JavaScript preloading
    this.addFontCSS();

    // Quick timeout for font loading
    setTimeout(() => {
      this.onFontsLoaded();
    }, 100); // Much faster timeout
  }

  addFontCSS() {
    // Add optimized font CSS without blocking
    const style = document.createElement('style');
    style.textContent = `
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap');
    `;
    document.head.appendChild(style);
  }

  useFontLoadingAPI() {
    const fontPromises = [];

    this.fontsToLoad.forEach(font => {
      font.weights.forEach(weight => {
        const fontFace = new FontFace(
          font.family,
          `url(https://fonts.gstatic.com/s/${font.family.toLowerCase()}/v20/${font.family.toLowerCase()}-${weight}.woff2) format('woff2')`,
          {
            weight: weight,
            display: font.display
          }
        );

        fontPromises.push(fontFace.load());
        document.fonts.add(fontFace);
      });
    });

    // Wait for all fonts to load
    Promise.all(fontPromises)
      .then(() => {
        this.onFontsLoaded();
      })
      .catch((error) => {
        console.warn('Font loading failed:', error);
        // Still mark as loaded to prevent indefinite loading state
        setTimeout(() => this.onFontsLoaded(), 3000);
      });

    // Fallback timeout
    setTimeout(() => {
      if (document.documentElement.classList.contains(this.loadingClass)) {
        this.onFontsLoaded();
      }
    }, 5000);
  }

  useFallbackMethod() {
    // For browsers without Font Loading API
    const testString = 'BESbswy';
    const testSize = '72px';
    const fallbackFont = 'monospace';

    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.fontSize = testSize;
    container.style.fontFamily = fallbackFont;
    container.innerHTML = testString;
    document.body.appendChild(container);

    const fallbackWidth = container.offsetWidth;

    // Test each font
    let fontsLoaded = 0;
    const totalFonts = this.fontsToLoad.length;

    this.fontsToLoad.forEach(font => {
      const testElement = document.createElement('div');
      testElement.style.position = 'absolute';
      testElement.style.left = '-9999px';
      testElement.style.top = '-9999px';
      testElement.style.fontSize = testSize;
      testElement.style.fontFamily = `${font.family}, ${fallbackFont}`;
      testElement.innerHTML = testString;
      document.body.appendChild(testElement);

      const checkFont = () => {
        if (testElement.offsetWidth !== fallbackWidth) {
          fontsLoaded++;
          document.body.removeChild(testElement);

          if (fontsLoaded === totalFonts) {
            document.body.removeChild(container);
            this.onFontsLoaded();
          }
        } else {
          setTimeout(checkFont, 100);
        }
      };

      checkFont();
    });

    // Fallback timeout
    setTimeout(() => {
      if (document.documentElement.classList.contains(this.loadingClass)) {
        this.onFontsLoaded();
      }
    }, 5000);
  }

  onFontsLoaded() {
    document.documentElement.classList.remove(this.loadingClass);
    document.documentElement.classList.add(this.loadedClass);

    // Trigger a reflow to ensure proper sizing
    this.triggerReflow();

    // Dispatch custom event
    const event = new CustomEvent('fontsLoaded', {
      detail: { optimizer: this }
    });
    document.dispatchEvent(event);

    console.log('✅ Fonts loaded successfully - layout shifts prevented');
  }

  preventLayoutShifts() {
    // Add minimal CSS to prevent layout shifts without conflicts
    const style = document.createElement('style');
    style.id = 'font-loading-optimizer-styles';
    style.textContent = `
      /* Minimal layout shift prevention */
      h1, h2, h3, h4, h5, h6 {
        min-height: 1.2em;
        font-size-adjust: 0.5;
      }

      header h1 {
        min-height: 1.5em;
        line-height: 1.2;
      }
    `;

    document.head.appendChild(style);
  }

  triggerReflow() {
    // Force a reflow to ensure proper sizing after font loading
    const headers = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headers.forEach(header => {
      const display = header.style.display;
      header.style.display = 'none';
      header.offsetHeight; // Trigger reflow
      header.style.display = display;
    });
  }

  // Public method to check if fonts are loaded
  areFontsLoaded() {
    return document.documentElement.classList.contains(this.loadedClass);
  }

  // Public method to manually trigger font loading check
  checkFontLoading() {
    if ('fonts' in document) {
      return document.fonts.ready.then(() => {
        if (!this.areFontsLoaded()) {
          this.onFontsLoaded();
        }
      });
    }
    return Promise.resolve();
  }
}

// Initialize font loading optimizer immediately
const fontOptimizer = new FontLoadingOptimizer();

// Make it globally available
window.fontLoadingOptimizer = fontOptimizer;

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FontLoadingOptimizer;
}
