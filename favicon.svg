<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fc5c7d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6a82fb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#gradient)" stroke="#fff" stroke-width="1"/>
  
  <!-- Sisters symbol - stylized "S" -->
  <path d="M12 8 C8 8, 6 10, 6 13 C6 15, 8 16, 10 16 L14 16 C16 16, 18 17, 18 19 C18 21, 16 22, 14 22 L10 22 C9 22, 8 21, 8 20" 
        fill="none" stroke="#fff" stroke-width="2.5" stroke-linecap="round"/>
  
  <!-- Heart symbol for "Good" -->
  <path d="M20 12 C20 10, 22 9, 23 10 C24 9, 26 10, 26 12 C26 14, 23 17, 23 17 C23 17, 20 14, 20 12 Z" 
        fill="#fff"/>
</svg>
