/**
 * Sisters for Good - Testimonials Carousel
 * Handles testimonial carousel functionality
 */

document.addEventListener('DOMContentLoaded', function() {
  initTestimonialsCarousel();
});

function initTestimonialsCarousel() {
  const slides = document.querySelectorAll('.testimonial-slide');
  const dots = document.querySelectorAll('.carousel-dot');
  const prevButton = document.getElementById('prev-testimonial');
  const nextButton = document.getElementById('next-testimonial');
  
  if (!slides.length || !dots.length) return;
  
  let currentSlide = 0;
  const slideCount = slides.length;
  
  // Initialize carousel
  showSlide(currentSlide);
  
  // Add event listeners
  if (prevButton) {
    prevButton.addEventListener('click', () => {
      currentSlide = (currentSlide - 1 + slideCount) % slideCount;
      showSlide(currentSlide);
    });
  }
  
  if (nextButton) {
    nextButton.addEventListener('click', () => {
      currentSlide = (currentSlide + 1) % slideCount;
      showSlide(currentSlide);
    });
  }
  
  // Add event listeners to dots
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      currentSlide = index;
      showSlide(currentSlide);
    });
  });
  
  // Auto-advance slides every 5 seconds
  setInterval(() => {
    currentSlide = (currentSlide + 1) % slideCount;
    showSlide(currentSlide);
  }, 5000);
  
  // Show the specified slide
  function showSlide(index) {
    // Hide all slides
    slides.forEach(slide => {
      slide.classList.remove('active');
    });
    
    // Deactivate all dots
    dots.forEach(dot => {
      dot.classList.remove('active');
    });
    
    // Show the current slide
    slides[index].classList.add('active');
    
    // Activate the current dot
    dots[index].classList.add('active');
  }
}
