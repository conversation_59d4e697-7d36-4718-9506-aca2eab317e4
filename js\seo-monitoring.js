/**
 * SEO Monitoring System
 * Advanced SEO tracking, ranking monitoring, and optimization recommendations
 */

class SEOMonitoringSystem {
  constructor() {
    this.seoData = {
      rankings: new Map(),
      keywords: [],
      backlinks: [],
      technicalSEO: new Map(),
      contentAnalysis: new Map(),
      competitorData: new Map()
    };
    this.config = {
      targetKeywords: [
        'sisters for good',
        'women empowerment pottstown',
        'cosmetology education pa',
        'community support pottstown',
        'nonprofit women pennsylvania',
        'back to school hair event',
        'fathers day brunch pottstown',
        'tobacco free living education'
      ],
      competitors: [
        'local-nonprofits.org',
        'community-centers-pa.org',
        'women-empowerment-groups.org'
      ]
    };
    this.init();
  }

  init() {
    this.setupSEOTracking();
    this.analyzeTechnicalSEO();
    this.monitorPagePerformance();
    this.trackContentOptimization();
    this.setupRankingMonitoring();
  }

  setupSEOTracking() {
    // Track SEO-relevant user interactions
    document.addEventListener('DOMContentLoaded', () => {
      this.analyzePageSEO();
      this.trackSEOMetrics();
      this.monitorStructuredData();
    });

    // Track external link clicks for backlink analysis
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href^="http"]');
      if (link && !link.href.includes(window.location.hostname)) {
        this.trackExternalLinkClick(link.href);
      }
    });

    // Monitor search engine bot visits
    this.detectSearchEngineBots();
  }

  analyzePageSEO() {
    const analysis = {
      url: window.location.href,
      title: document.title,
      titleLength: document.title.length,
      metaDescription: this.getMetaDescription(),
      metaDescriptionLength: this.getMetaDescription().length,
      headings: this.analyzeHeadings(),
      images: this.analyzeImages(),
      links: this.analyzeLinks(),
      keywords: this.extractKeywords(),
      readabilityScore: this.calculateReadabilityScore(),
      pageSpeed: this.getPageSpeedMetrics(),
      mobileOptimization: this.checkMobileOptimization(),
      structuredData: this.analyzeStructuredData(),
      timestamp: Date.now()
    };

    this.seoData.contentAnalysis.set(window.location.pathname, analysis);
    this.storeSEOData();

    // Generate SEO recommendations
    const recommendations = this.generateSEORecommendations(analysis);
    this.displaySEORecommendations(recommendations);

    return analysis;
  }

  analyzeHeadings() {
    const headings = {
      h1: Array.from(document.querySelectorAll('h1')).map(h => h.textContent.trim()),
      h2: Array.from(document.querySelectorAll('h2')).map(h => h.textContent.trim()),
      h3: Array.from(document.querySelectorAll('h3')).map(h => h.textContent.trim()),
      h4: Array.from(document.querySelectorAll('h4')).map(h => h.textContent.trim()),
      h5: Array.from(document.querySelectorAll('h5')).map(h => h.textContent.trim()),
      h6: Array.from(document.querySelectorAll('h6')).map(h => h.textContent.trim())
    };

    return {
      ...headings,
      hierarchy: this.checkHeadingHierarchy(),
      keywordOptimization: this.checkHeadingKeywords(headings)
    };
  }

  analyzeImages() {
    const images = Array.from(document.querySelectorAll('img'));
    return {
      total: images.length,
      withAlt: images.filter(img => img.alt && img.alt.trim()).length,
      withoutAlt: images.filter(img => !img.alt || !img.alt.trim()).length,
      withTitle: images.filter(img => img.title && img.title.trim()).length,
      optimizedAlt: images.filter(img => this.isOptimizedAltText(img.alt)).length,
      lazyLoaded: images.filter(img => img.loading === 'lazy' || img.hasAttribute('data-src')).length
    };
  }

  analyzeLinks() {
    const links = Array.from(document.querySelectorAll('a[href]'));
    const internal = links.filter(link => this.isInternalLink(link.href));
    const external = links.filter(link => !this.isInternalLink(link.href));

    return {
      total: links.length,
      internal: internal.length,
      external: external.length,
      nofollow: links.filter(link => link.rel && link.rel.includes('nofollow')).length,
      withoutText: links.filter(link => !link.textContent.trim()).length,
      brokenLinks: this.detectBrokenLinks(links)
    };
  }

  extractKeywords() {
    const content = document.body.textContent.toLowerCase();
    const words = content.match(/\b\w{3,}\b/g) || [];
    const wordCount = {};

    words.forEach(word => {
      if (!this.isStopWord(word)) {
        wordCount[word] = (wordCount[word] || 0) + 1;
      }
    });

    // Sort by frequency and return top keywords
    return Object.entries(wordCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([word, count]) => ({ word, count, density: (count / words.length * 100).toFixed(2) }));
  }

  calculateReadabilityScore() {
    const content = document.body.textContent;
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = content.split(/\s+/).filter(w => w.trim().length > 0);
    const syllables = words.reduce((total, word) => total + this.countSyllables(word), 0);

    if (sentences.length === 0 || words.length === 0) return 0;

    // Flesch Reading Ease Score
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  getPageSpeedMetrics() {
    const navigation = performance.getEntriesByType('navigation')[0];
    if (!navigation) return null;

    return {
      loadTime: navigation.loadEventEnd - navigation.fetchStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
      firstByte: navigation.responseStart - navigation.requestStart,
      domInteractive: navigation.domInteractive - navigation.fetchStart
    };
  }

  checkMobileOptimization() {
    const viewport = document.querySelector('meta[name="viewport"]');
    const hasResponsiveImages = document.querySelectorAll('img[srcset]').length > 0;
    const hasTouchTargets = this.checkTouchTargetSizes();

    return {
      hasViewportMeta: !!viewport,
      viewportContent: viewport ? viewport.content : null,
      hasResponsiveImages: hasResponsiveImages,
      hasTouchTargets: hasTouchTargets,
      mobileScore: this.calculateMobileScore(viewport, hasResponsiveImages, hasTouchTargets)
    };
  }

  analyzeStructuredData() {
    const jsonLdScripts = Array.from(document.querySelectorAll('script[type="application/ld+json"]'));
    const structuredData = [];

    jsonLdScripts.forEach(script => {
      try {
        const data = JSON.parse(script.textContent);
        structuredData.push(data);
      } catch (e) {
        console.warn('Invalid JSON-LD structured data:', e);
      }
    });

    return {
      hasStructuredData: structuredData.length > 0,
      types: structuredData.map(data => data['@type']).filter(Boolean),
      schemas: structuredData,
      organizationSchema: structuredData.find(data => data['@type'] === 'Organization'),
      localBusinessSchema: structuredData.find(data => data['@type'] === 'LocalBusiness')
    };
  }

  generateSEORecommendations(analysis) {
    const recommendations = [];

    // Title optimization
    if (analysis.titleLength < 30) {
      recommendations.push({
        type: 'title',
        priority: 'high',
        message: 'Title is too short. Consider expanding to 50-60 characters for better SEO.',
        current: analysis.titleLength,
        recommended: '50-60 characters'
      });
    } else if (analysis.titleLength > 60) {
      recommendations.push({
        type: 'title',
        priority: 'medium',
        message: 'Title may be too long and could be truncated in search results.',
        current: analysis.titleLength,
        recommended: '50-60 characters'
      });
    }

    // Meta description optimization
    if (analysis.metaDescriptionLength < 120) {
      recommendations.push({
        type: 'meta-description',
        priority: 'high',
        message: 'Meta description is too short. Expand to 150-160 characters.',
        current: analysis.metaDescriptionLength,
        recommended: '150-160 characters'
      });
    } else if (analysis.metaDescriptionLength > 160) {
      recommendations.push({
        type: 'meta-description',
        priority: 'medium',
        message: 'Meta description may be too long and could be truncated.',
        current: analysis.metaDescriptionLength,
        recommended: '150-160 characters'
      });
    }

    // Heading optimization
    if (analysis.headings.h1.length === 0) {
      recommendations.push({
        type: 'headings',
        priority: 'high',
        message: 'Page is missing an H1 tag. Add a descriptive H1 heading.',
        current: 'No H1 tag',
        recommended: 'Add H1 tag'
      });
    } else if (analysis.headings.h1.length > 1) {
      recommendations.push({
        type: 'headings',
        priority: 'medium',
        message: 'Multiple H1 tags found. Use only one H1 per page.',
        current: `${analysis.headings.h1.length} H1 tags`,
        recommended: '1 H1 tag'
      });
    }

    // Image optimization
    if (analysis.images.withoutAlt > 0) {
      recommendations.push({
        type: 'images',
        priority: 'high',
        message: `${analysis.images.withoutAlt} images missing alt text. Add descriptive alt attributes.`,
        current: `${analysis.images.withoutAlt} images without alt`,
        recommended: 'All images should have alt text'
      });
    }

    // Page speed optimization
    if (analysis.pageSpeed && analysis.pageSpeed.loadTime > 3000) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        message: 'Page load time is slow. Optimize images, minify CSS/JS, and enable caching.',
        current: `${Math.round(analysis.pageSpeed.loadTime)}ms`,
        recommended: '< 3000ms'
      });
    }

    // Mobile optimization
    if (!analysis.mobileOptimization.hasViewportMeta) {
      recommendations.push({
        type: 'mobile',
        priority: 'high',
        message: 'Missing viewport meta tag. Add viewport meta tag for mobile optimization.',
        current: 'No viewport meta',
        recommended: 'Add viewport meta tag'
      });
    }

    // Readability
    if (analysis.readabilityScore < 60) {
      recommendations.push({
        type: 'content',
        priority: 'medium',
        message: 'Content readability could be improved. Use shorter sentences and simpler words.',
        current: `Readability score: ${analysis.readabilityScore}`,
        recommended: 'Score > 60'
      });
    }

    return recommendations;
  }

  displaySEORecommendations(recommendations) {
    if (recommendations.length === 0 || !this.isAdminUser()) return;

    // Create SEO recommendations panel
    const panel = document.createElement('div');
    panel.id = 'seo-recommendations-panel';
    panel.className = 'fixed bottom-20 right-4 z-40 bg-white dark:bg-slate-800 rounded-lg shadow-lg max-w-sm p-4 border-l-4 border-blue-500';
    panel.innerHTML = `
      <div class="flex items-center justify-between mb-3">
        <h4 class="font-bold text-gray-900 dark:text-white text-sm">SEO Recommendations</h4>
        <button onclick="this.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div class="space-y-2 max-h-64 overflow-y-auto">
        ${recommendations.slice(0, 5).map(rec => `
          <div class="p-2 bg-gray-50 dark:bg-slate-700 rounded text-xs">
            <div class="flex items-center mb-1">
              <span class="w-2 h-2 rounded-full mr-2 ${
                rec.priority === 'high' ? 'bg-red-500' :
                rec.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
              }"></span>
              <span class="font-medium text-gray-900 dark:text-white">${rec.type.toUpperCase()}</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300">${rec.message}</p>
          </div>
        `).join('')}
      </div>
      ${recommendations.length > 5 ? `
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
          +${recommendations.length - 5} more recommendations
        </p>
      ` : ''}
    `;

    document.body.appendChild(panel);

    // Auto-hide after 10 seconds
    setTimeout(() => {
      panel.remove();
    }, 10000);
  }

  trackSEOMetrics() {
    // Track SEO-relevant metrics
    const metrics = {
      page: window.location.pathname,
      timestamp: Date.now(),
      organicTraffic: this.isOrganicTraffic(),
      searchEngine: this.getSearchEngine(document.referrer),
      keywords: this.extractSearchKeywords(document.referrer),
      rankingPosition: this.estimateRankingPosition(),
      clickThroughRate: this.calculateCTR()
    };

    this.seoData.technicalSEO.set(window.location.pathname, metrics);
    this.storeSEOData();
  }

  setupRankingMonitoring() {
    // Monitor search engine rankings for target keywords
    this.config.targetKeywords.forEach(keyword => {
      this.trackKeywordRanking(keyword);
    });
  }

  trackKeywordRanking(keyword) {
    // Simulate ranking tracking (in production, integrate with SEO APIs)
    const ranking = {
      keyword: keyword,
      position: Math.floor(Math.random() * 50) + 1, // Simulated ranking
      url: window.location.href,
      searchEngine: 'google',
      timestamp: Date.now(),
      previousPosition: this.getPreviousRanking(keyword)
    };

    this.seoData.rankings.set(keyword, ranking);
    this.storeSEOData();
  }

  // Utility methods
  getMetaDescription() {
    const metaDesc = document.querySelector('meta[name="description"]');
    return metaDesc ? metaDesc.content : '';
  }

  checkHeadingHierarchy() {
    const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
    const hierarchy = [];
    let previousLevel = 0;

    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1));
      if (level > previousLevel + 1) {
        hierarchy.push({
          element: heading.tagName,
          issue: 'Skipped heading level',
          text: heading.textContent.trim()
        });
      }
      previousLevel = level;
    });

    return hierarchy;
  }

  checkHeadingKeywords(headings) {
    const targetKeywords = this.config.targetKeywords;
    const optimizedHeadings = [];

    Object.entries(headings).forEach(([level, headingTexts]) => {
      headingTexts.forEach(text => {
        const hasKeyword = targetKeywords.some(keyword =>
          text.toLowerCase().includes(keyword.toLowerCase())
        );
        if (hasKeyword) {
          optimizedHeadings.push({ level, text, optimized: true });
        }
      });
    });

    return optimizedHeadings;
  }

  isOptimizedAltText(altText) {
    if (!altText || altText.trim().length < 5) return false;

    // Check if alt text contains target keywords
    return this.config.targetKeywords.some(keyword =>
      altText.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  isInternalLink(href) {
    try {
      const url = new URL(href, window.location.origin);
      return url.hostname === window.location.hostname;
    } catch {
      return href.startsWith('/') || href.startsWith('./') || href.startsWith('../');
    }
  }

  detectBrokenLinks(links) {
    // In production, this would check link status
    return []; // Placeholder
  }

  isStopWord(word) {
    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];
    return stopWords.includes(word.toLowerCase());
  }

  countSyllables(word) {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
    word = word.replace(/^y/, '');
    const matches = word.match(/[aeiouy]{1,2}/g);
    return matches ? matches.length : 1;
  }

  checkTouchTargetSizes() {
    const clickableElements = document.querySelectorAll('a, button, input, select, textarea');
    let adequateTargets = 0;

    clickableElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      if (rect.width >= 44 && rect.height >= 44) {
        adequateTargets++;
      }
    });

    return adequateTargets / clickableElements.length;
  }

  calculateMobileScore(viewport, hasResponsiveImages, touchTargetRatio) {
    let score = 0;
    if (viewport) score += 40;
    if (hasResponsiveImages) score += 30;
    if (touchTargetRatio > 0.8) score += 30;
    return score;
  }

  detectSearchEngineBots() {
    const userAgent = navigator.userAgent.toLowerCase();
    const bots = ['googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider'];

    const isBot = bots.some(bot => userAgent.includes(bot));
    if (isBot) {
      this.trackBotVisit(userAgent);
    }
  }

  trackBotVisit(userAgent) {
    const botVisits = JSON.parse(localStorage.getItem('sfg-bot-visits') || '[]');
    botVisits.push({
      userAgent: userAgent,
      page: window.location.pathname,
      timestamp: Date.now()
    });

    // Keep only last 100 bot visits
    if (botVisits.length > 100) {
      botVisits.shift();
    }

    localStorage.setItem('sfg-bot-visits', JSON.stringify(botVisits));
  }

  trackExternalLinkClick(href) {
    if (window.analyticsSystem) {
      window.analyticsSystem.trackEvent('external_link_click', {
        destination: href,
        page_location: window.location.pathname
      });
    }
  }

  isOrganicTraffic() {
    const referrer = document.referrer;
    const searchEngines = ['google', 'bing', 'yahoo', 'duckduckgo'];
    return searchEngines.some(engine => referrer.includes(engine));
  }

  getSearchEngine(referrer) {
    if (!referrer) return null;
    if (referrer.includes('google')) return 'google';
    if (referrer.includes('bing')) return 'bing';
    if (referrer.includes('yahoo')) return 'yahoo';
    if (referrer.includes('duckduckgo')) return 'duckduckgo';
    return 'other';
  }

  extractSearchKeywords(referrer) {
    // Extract search keywords from referrer URL
    try {
      const url = new URL(referrer);
      return url.searchParams.get('q') || url.searchParams.get('query') || null;
    } catch {
      return null;
    }
  }

  estimateRankingPosition() {
    // Estimate ranking position based on referrer and click position
    return Math.floor(Math.random() * 10) + 1; // Placeholder
  }

  calculateCTR() {
    // Calculate click-through rate from search results
    return (Math.random() * 10).toFixed(2); // Placeholder
  }

  getPreviousRanking(keyword) {
    const previous = this.seoData.rankings.get(keyword);
    return previous ? previous.position : null;
  }

  isAdminUser() {
    // Use global admin check function if available
    if (typeof window.isAdminUser === 'function') {
      return window.isAdminUser();
    }

    // Fallback to existing methods
    return localStorage.getItem('sfg-admin-access') === 'true' ||
           sessionStorage.getItem('sfg-admin-session') === 'true' ||
           window.location.search.includes('admin=true');
  }

  storeSEOData() {
    const seoDataToStore = {
      rankings: Array.from(this.seoData.rankings.entries()),
      technicalSEO: Array.from(this.seoData.technicalSEO.entries()),
      contentAnalysis: Array.from(this.seoData.contentAnalysis.entries()),
      lastUpdated: Date.now()
    };

    localStorage.setItem('sfg-seo-data', JSON.stringify(seoDataToStore));
  }

  getSEOReport() {
    return {
      currentPage: this.seoData.contentAnalysis.get(window.location.pathname),
      rankings: Object.fromEntries(this.seoData.rankings),
      technicalSEO: Object.fromEntries(this.seoData.technicalSEO),
      recommendations: this.generateSEORecommendations(
        this.seoData.contentAnalysis.get(window.location.pathname) || {}
      )
    };
  }
}

// Initialize SEO monitoring system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.seoMonitoring = new SEOMonitoringSystem();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SEOMonitoringSystem;
}
