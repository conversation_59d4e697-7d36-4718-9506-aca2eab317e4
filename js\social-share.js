/**
 * Sisters for Good - Social Sharing
 * Handles social media sharing functionality
 */

// Share on Facebook
function shareOnFacebook() {
  const url = encodeURIComponent(window.location.href);
  const title = encodeURIComponent(document.title);
  window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
}

// Share on Twitter
function shareOnTwitter() {
  const url = encodeURIComponent(window.location.href);
  const title = encodeURIComponent(document.title);
  window.open(`https://twitter.com/intent/tweet?text=${title}&url=${url}`, '_blank');
}

// Share on LinkedIn
function shareOnLinkedIn() {
  const url = encodeURIComponent(window.location.href);
  const title = encodeURIComponent(document.title);
  window.open(`https://www.linkedin.com/shareArticle?mini=true&url=${url}&title=${title}`, '_blank');
}

// Share by Email
function shareByEmail() {
  const url = window.location.href;
  const title = document.title;
  const body = `I thought you might be interested in this: ${title}\n\n${url}`;
  window.location.href = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(body)}`;
}

// Load social sharing component
function loadSocialSharingComponent() {
  const socialShareContainers = document.querySelectorAll('.social-share-placeholder');
  
  if (!socialShareContainers.length) return;
  
  // Fetch social sharing component
  fetch('components/social-share.html')
    .then(response => response.text())
    .then(data => {
      socialShareContainers.forEach(container => {
        container.innerHTML = data;
      });
    })
    .catch(error => {
      console.error('Error loading social sharing component:', error);
      socialShareContainers.forEach(container => {
        container.innerHTML = '<p class="text-center">Failed to load sharing options.</p>';
      });
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  loadSocialSharingComponent();
});
