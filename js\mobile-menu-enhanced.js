/**
 * Enhanced Mobile Menu System
 * Provides improved mobile navigation with better organization and touch targets
 */

class EnhancedMobileMenu {
  constructor() {
    this.isOpen = false;
    this.menuButton = null;
    this.mobileMenu = null;
    this.init();
  }

  init() {
    this.enhanceExistingMenu();
    this.setupEventListeners();
    this.addSearchToMobileMenu();
    this.addThemeToggleToMobileMenu();
  }

  enhanceExistingMenu() {
    // Find existing mobile menu elements
    this.menuButton = document.getElementById('mobileMenuBtn');
    this.mobileMenu = document.getElementById('mobileMenu');

    if (!this.menuButton || !this.mobileMenu) return;

    // Enhance menu button with better accessibility
    this.menuButton.setAttribute('aria-expanded', 'false');
    this.menuButton.setAttribute('aria-controls', 'mobileMenu');
    this.menuButton.setAttribute('aria-label', 'Toggle navigation menu');

    // Enhance mobile menu structure
    this.enhanceMobileMenuStructure();
  }

  enhanceMobileMenuStructure() {
    if (!this.mobileMenu) return;

    // Get current navigation links
    const navLinks = this.mobileMenu.querySelectorAll('a.nav-link');
    
    // Create enhanced menu structure
    const enhancedMenuHTML = `
      <div class="mobile-menu-content bg-white dark:bg-slate-800 rounded-lg shadow-lg mx-2 mt-2 overflow-hidden">
        <!-- Search Section -->
        <div class="mobile-search-section p-4 border-b border-gray-200 dark:border-gray-600">
          <button id="mobile-search-btn" class="w-full flex items-center justify-center p-3 bg-gray-50 dark:bg-slate-700 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors">
            <svg class="h-5 w-5 text-gray-600 dark:text-gray-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <span class="text-gray-700 dark:text-gray-300 font-medium">Search Website</span>
          </button>
        </div>

        <!-- Main Navigation -->
        <div class="mobile-nav-section">
          <div class="p-2">
            <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3 py-2">Main Navigation</h3>
            <nav class="space-y-1">
              ${this.generateEnhancedNavLinks(navLinks)}
            </nav>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="mobile-actions-section border-t border-gray-200 dark:border-gray-600 p-2">
          <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3 py-2">Quick Actions</h3>
          <div class="grid grid-cols-2 gap-2">
            <a href="donate.html" class="flex flex-col items-center p-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all transform hover:scale-105">
              <svg class="h-6 w-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
              <span class="text-xs font-medium">Donate</span>
            </a>
            <a href="contact.html" class="flex flex-col items-center p-3 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-600 transition-all transform hover:scale-105">
              <svg class="h-6 w-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span class="text-xs font-medium">Contact</span>
            </a>
          </div>
        </div>

        <!-- Theme Toggle -->
        <div class="mobile-theme-section border-t border-gray-200 dark:border-gray-600 p-4">
          <button id="mobile-theme-toggle" class="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-slate-700 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors">
            <span class="text-gray-700 dark:text-gray-300 font-medium">Theme</span>
            <div class="flex items-center">
              <svg class="sun-icon h-5 w-5 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
              </svg>
              <svg class="moon-icon h-5 w-5 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
            </div>
          </button>
        </div>

        <!-- Contact Info -->
        <div class="mobile-contact-section border-t border-gray-200 dark:border-gray-600 p-4 bg-gray-50 dark:bg-slate-700">
          <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Need help?</p>
            <a href="tel:************" class="text-pink-600 dark:text-pink-400 font-semibold hover:underline">
              (*************
            </a>
          </div>
        </div>
      </div>
    `;

    this.mobileMenu.innerHTML = enhancedMenuHTML;
  }

  generateEnhancedNavLinks(navLinks) {
    const linkData = [
      { href: 'index.html', text: 'Home', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
      { href: 'about.html', text: 'About Us', icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z' },
      { href: 'programs.html', text: 'Programs', icon: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253' },
      { href: 'events.html', text: 'Events', icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' },
      { href: 'health.html', text: 'Health Resources', icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z' }
    ];

    return linkData.map(link => {
      const currentPage = window.location.pathname.split('/').pop() || 'index.html';
      const isActive = currentPage === link.href;
      
      return `
        <a href="${link.href}" class="mobile-nav-link flex items-center p-3 rounded-lg transition-all ${
          isActive 
            ? 'bg-pink-100 dark:bg-pink-900 text-pink-700 dark:text-pink-300 font-semibold' 
            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-600'
        }">
          <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${link.icon}"></path>
          </svg>
          <span class="font-medium">${link.text}</span>
          ${isActive ? '<div class="ml-auto w-2 h-2 bg-pink-500 rounded-full"></div>' : ''}
        </a>
      `;
    }).join('');
  }

  setupEventListeners() {
    if (!this.menuButton) return;

    // Menu toggle
    this.menuButton.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggleMenu();
    });

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
      if (this.isOpen && !this.mobileMenu.contains(e.target) && !this.menuButton.contains(e.target)) {
        this.closeMenu();
      }
    });

    // Close menu on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.closeMenu();
      }
    });

    // Handle mobile search button
    document.addEventListener('click', (e) => {
      if (e.target.closest('#mobile-search-btn')) {
        this.closeMenu();
        // Trigger search modal
        if (window.siteSearch) {
          window.siteSearch.openSearch();
        }
      }
    });

    // Handle mobile theme toggle
    document.addEventListener('click', (e) => {
      if (e.target.closest('#mobile-theme-toggle')) {
        // Trigger theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
          themeToggle.click();
        }
      }
    });
  }

  addSearchToMobileMenu() {
    // Search functionality is already included in the enhanced menu structure
  }

  addThemeToggleToMobileMenu() {
    // Theme toggle functionality is already included in the enhanced menu structure
  }

  toggleMenu() {
    if (this.isOpen) {
      this.closeMenu();
    } else {
      this.openMenu();
    }
  }

  openMenu() {
    if (!this.mobileMenu) return;

    this.isOpen = true;
    this.mobileMenu.classList.remove('hidden');
    this.menuButton.setAttribute('aria-expanded', 'true');
    
    // Update menu button icon to X
    this.updateMenuButtonIcon(true);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Add animation
    requestAnimationFrame(() => {
      this.mobileMenu.style.opacity = '1';
      this.mobileMenu.style.transform = 'translateY(0)';
    });
  }

  closeMenu() {
    if (!this.mobileMenu) return;

    this.isOpen = false;
    this.mobileMenu.classList.add('hidden');
    this.menuButton.setAttribute('aria-expanded', 'false');
    
    // Update menu button icon to hamburger
    this.updateMenuButtonIcon(false);
    
    // Restore body scroll
    document.body.style.overflow = '';

    // Reset animation
    this.mobileMenu.style.opacity = '';
    this.mobileMenu.style.transform = '';
  }

  updateMenuButtonIcon(isOpen) {
    const icon = this.menuButton.querySelector('svg');
    if (!icon) return;

    if (isOpen) {
      icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />';
    } else {
      icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />';
    }
  }
}

// Initialize enhanced mobile menu when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.enhancedMobileMenu = new EnhancedMobileMenu();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedMobileMenu;
}
