/**
 * Content Management System
 * Handles blog/news, resource library, photo gallery, and testimonials
 */

class ContentManagementSystem {
  constructor() {
    this.blogPosts = [];
    this.resources = [];
    this.photos = [];
    this.testimonials = [];
    this.init();
  }

  init() {
    this.loadContent();
    this.setupEventListeners();
    this.initializeComponents();
  }

  loadContent() {
    // Load content from localStorage or API
    this.loadBlogPosts();
    this.loadResources();
    this.loadPhotos();
    this.loadTestimonials();
  }

  loadBlogPosts() {
    const storedPosts = localStorage.getItem('sisters-for-good-blog-posts');
    if (storedPosts) {
      this.blogPosts = JSON.parse(storedPosts);
    } else {
      // Default blog posts
      this.blogPosts = [
        {
          id: 'post-1',
          title: 'Empowering Women Through Cosmetology Education',
          excerpt: 'Discover how our cosmetology education program is transforming lives and creating opportunities for women in our community.',
          content: 'Our cosmetology education program has been a cornerstone of Sisters for Good since our founding. Through partnerships with local beauty schools and professional mentors, we provide comprehensive training that goes beyond technical skills...',
          author: '<PERSON><PERSON>',
          publishDate: '2024-11-15',
          category: 'Education',
          tags: ['cosmetology', 'education', 'women empowerment'],
          image: 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=800&q=80',
          featured: true,
          status: 'published'
        },
        {
          id: 'post-2',
          title: 'Community Impact: Back to School Hair Event Success',
          excerpt: 'Over 200 children received free haircuts and styling services at our annual Back to School Hair Event.',
          content: 'This year\'s Back to School Hair Event was our most successful yet, serving over 200 children from families throughout Pottstown and surrounding areas...',
          author: 'Sisters for Good Team',
          publishDate: '2024-08-20',
          category: 'Community Events',
          tags: ['back to school', 'community service', 'children'],
          image: 'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?w=800&q=80',
          featured: false,
          status: 'published'
        },
        {
          id: 'post-3',
          title: 'Building Stronger Communities Through Collaboration',
          excerpt: 'Learn about our partnerships with local organizations and how collaboration amplifies our impact.',
          content: 'At Sisters for Good, we believe that the strongest communities are built through collaboration and mutual support...',
          author: 'Sheryl Williams',
          publishDate: '2024-10-05',
          category: 'Community',
          tags: ['collaboration', 'partnerships', 'community building'],
          image: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=800&q=80',
          featured: false,
          status: 'published'
        }
      ];
      this.saveBlogPosts();
    }
  }

  loadResources() {
    const storedResources = localStorage.getItem('sisters-for-good-resources');
    if (storedResources) {
      this.resources = JSON.parse(storedResources);
    } else {
      // Default resources
      this.resources = [
        {
          id: 'resource-1',
          title: 'Cosmetology Career Guide',
          description: 'Complete guide to starting a career in cosmetology, including licensing requirements and job opportunities.',
          type: 'PDF',
          category: 'Education',
          downloadUrl: '#',
          fileSize: '2.5 MB',
          downloadCount: 156,
          uploadDate: '2024-09-15',
          tags: ['cosmetology', 'career', 'guide']
        },
        {
          id: 'resource-2',
          title: 'Financial Literacy Workbook',
          description: 'Interactive workbook covering budgeting, saving, and financial planning basics.',
          type: 'PDF',
          category: 'Financial',
          downloadUrl: '#',
          fileSize: '1.8 MB',
          downloadCount: 89,
          uploadDate: '2024-10-01',
          tags: ['financial literacy', 'budgeting', 'planning']
        },
        {
          id: 'resource-3',
          title: 'Community Health Resources Directory',
          description: 'Comprehensive directory of local health services, clinics, and support programs.',
          type: 'PDF',
          category: 'Health',
          downloadUrl: '#',
          fileSize: '3.2 MB',
          downloadCount: 234,
          uploadDate: '2024-08-10',
          tags: ['health', 'directory', 'community resources']
        },
        {
          id: 'resource-4',
          title: 'Job Interview Preparation Checklist',
          description: 'Step-by-step checklist to help you prepare for successful job interviews.',
          type: 'PDF',
          category: 'Employment',
          downloadUrl: '#',
          fileSize: '0.8 MB',
          downloadCount: 67,
          uploadDate: '2024-11-01',
          tags: ['job interview', 'employment', 'career preparation']
        }
      ];
      this.saveResources();
    }
  }

  loadPhotos() {
    const storedPhotos = localStorage.getItem('sisters-for-good-photos');
    if (storedPhotos) {
      this.photos = JSON.parse(storedPhotos);
    } else {
      // Default photo gallery
      this.photos = [
        {
          id: 'photo-1',
          title: 'Father\'s Day Brunch 2024',
          description: 'Community members celebrating fathers at our annual brunch event.',
          url: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&q=80',
          thumbnail: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=300&q=80',
          category: 'Events',
          uploadDate: '2024-06-16',
          photographer: 'Sisters for Good Team',
          tags: ['fathers day', 'community', 'celebration']
        },
        {
          id: 'photo-2',
          title: 'Cosmetology Workshop',
          description: 'Students learning advanced hair styling techniques.',
          url: 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=800&q=80',
          thumbnail: 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=300&q=80',
          category: 'Education',
          uploadDate: '2024-09-10',
          photographer: 'Maria Rodriguez',
          tags: ['cosmetology', 'education', 'workshop']
        },
        {
          id: 'photo-3',
          title: 'Community Volunteers',
          description: 'Volunteers preparing for the Thanksgiving food drive.',
          url: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=800&q=80',
          thumbnail: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=300&q=80',
          category: 'Volunteers',
          uploadDate: '2024-11-15',
          photographer: 'John Smith',
          tags: ['volunteers', 'thanksgiving', 'community service']
        },
        {
          id: 'photo-4',
          title: 'Back to School Event',
          description: 'Children receiving free haircuts before the school year.',
          url: 'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?w=800&q=80',
          thumbnail: 'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?w=300&q=80',
          category: 'Events',
          uploadDate: '2024-08-20',
          photographer: 'Sisters for Good Team',
          tags: ['back to school', 'children', 'community service']
        }
      ];
      this.savePhotos();
    }
  }

  loadTestimonials() {
    const storedTestimonials = localStorage.getItem('sisters-for-good-testimonials');
    if (storedTestimonials) {
      this.testimonials = JSON.parse(storedTestimonials);
    } else {
      // Default testimonials
      this.testimonials = [
        {
          id: 'testimonial-1',
          name: 'Maria Rodriguez',
          role: 'Cosmetology Graduate',
          content: 'Sisters for Good changed my life. Through their cosmetology program, I gained the skills and confidence to start my own salon. The mentorship and support I received was invaluable.',
          rating: 5,
          photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&q=80',
          submitDate: '2024-10-15',
          featured: true,
          status: 'approved'
        },
        {
          id: 'testimonial-2',
          name: 'Jennifer Thompson',
          role: 'Workshop Participant',
          content: 'The financial literacy workshop opened my eyes to so many opportunities. I finally understand how to budget and save for my family\'s future. Thank you, Sisters for Good!',
          rating: 5,
          photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&q=80',
          submitDate: '2024-09-22',
          featured: true,
          status: 'approved'
        },
        {
          id: 'testimonial-3',
          name: 'David Williams',
          role: 'Community Member',
          content: 'As a father in the community, I appreciate everything Sisters for Good does for our families. The Father\'s Day brunch was such a meaningful celebration.',
          rating: 5,
          photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&q=80',
          submitDate: '2024-06-20',
          featured: false,
          status: 'approved'
        }
      ];
      this.saveTestimonials();
    }
  }

  setupEventListeners() {
    // Blog post interactions
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('read-more-btn')) {
        const postId = e.target.getAttribute('data-post-id');
        this.showFullBlogPost(postId);
      }

      if (e.target.classList.contains('resource-download-btn')) {
        const resourceId = e.target.getAttribute('data-resource-id');
        this.downloadResource(resourceId);
      }

      if (e.target.classList.contains('photo-view-btn')) {
        const photoId = e.target.getAttribute('data-photo-id');
        this.showPhotoModal(photoId);
      }

      if (e.target.classList.contains('submit-testimonial-btn')) {
        this.showTestimonialForm();
      }
    });

    // Search and filter functionality
    document.addEventListener('input', (e) => {
      if (e.target.id === 'blog-search') {
        this.filterBlogPosts(e.target.value);
      }
      if (e.target.id === 'resource-search') {
        this.filterResources(e.target.value);
      }
      if (e.target.id === 'photo-search') {
        this.filterPhotos(e.target.value);
      }
    });

    // Category filters
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('category-filter')) {
        this.applyFilters();
      }
    });
  }

  initializeComponents() {
    // Initialize blog component if present
    if (document.getElementById('blog-container')) {
      this.renderBlogPosts();
    }

    // Initialize resource library if present
    if (document.getElementById('resource-library-container')) {
      this.renderResources();
    }

    // Initialize photo gallery if present
    if (document.getElementById('photo-gallery-container')) {
      this.renderPhotoGallery();
    }

    // Initialize testimonials if present
    if (document.getElementById('testimonials-container')) {
      this.renderTestimonials();
    }
  }

  renderBlogPosts(posts = this.blogPosts) {
    const container = document.getElementById('blog-posts-grid');
    if (!container) return;

    const publishedPosts = posts.filter(post => post.status === 'published');

    container.innerHTML = publishedPosts.map(post => this.createBlogPostCard(post)).join('');
  }

  createBlogPostCard(post) {
    return `
      <article class="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
        <div class="relative h-48 overflow-hidden">
          <img src="${post.image}" alt="${post.title}" class="w-full h-full object-cover transition-transform duration-300 hover:scale-105" loading="lazy">
          <div class="absolute top-4 left-4">
            <span class="bg-pink-600 text-white px-3 py-1 rounded-full text-sm font-medium">
              ${post.category}
            </span>
          </div>
          ${post.featured ? '<div class="absolute top-4 right-4 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold">Featured</div>' : ''}
        </div>

        <div class="p-6">
          <div class="flex items-center text-sm text-gray-600 dark:text-gray-300 mb-3">
            <span>${post.author}</span>
            <span class="mx-2">•</span>
            <span>${new Date(post.publishDate).toLocaleDateString()}</span>
          </div>

          <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3 line-clamp-2">${post.title}</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">${post.excerpt}</p>

          <div class="flex flex-wrap gap-2 mb-4">
            ${post.tags.slice(0, 3).map(tag => `
              <span class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs">
                ${tag}
              </span>
            `).join('')}
          </div>

          <button class="read-more-btn bg-pink-600 text-white py-2 px-4 rounded-lg hover:bg-pink-700 transition-colors font-medium" data-post-id="${post.id}">
            Read More
          </button>
        </div>
      </article>
    `;
  }

  renderResources(resources = this.resources) {
    const container = document.getElementById('resources-grid');
    if (!container) return;

    container.innerHTML = resources.map(resource => this.createResourceCard(resource)).join('');
  }

  createResourceCard(resource) {
    return `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all duration-300">
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center">
            <div class="text-3xl mr-3">${this.getResourceIcon(resource.type)}</div>
            <div>
              <h3 class="text-lg font-bold text-gray-900 dark:text-white">${resource.title}</h3>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs mr-2">
                  ${resource.category}
                </span>
                <span>${resource.fileSize}</span>
              </div>
            </div>
          </div>
        </div>

        <p class="text-gray-600 dark:text-gray-300 mb-4">${resource.description}</p>

        <div class="flex flex-wrap gap-2 mb-4">
          ${resource.tags.map(tag => `
            <span class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs">
              ${tag}
            </span>
          `).join('')}
        </div>

        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            <span>📥 ${resource.downloadCount} downloads</span>
          </div>
          <button class="resource-download-btn bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium" data-resource-id="${resource.id}">
            Download
          </button>
        </div>
      </div>
    `;
  }

  getResourceIcon(type) {
    const icons = {
      'PDF': '📄',
      'DOC': '📝',
      'XLS': '📊',
      'PPT': '📋',
      'VIDEO': '🎥',
      'AUDIO': '🎵'
    };
    return icons[type] || '📄';
  }

  renderPhotoGallery(photos = this.photos) {
    const container = document.getElementById('photo-gallery-grid');
    if (!container) return;

    container.innerHTML = photos.map(photo => this.createPhotoCard(photo)).join('');
  }

  createPhotoCard(photo) {
    return `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
        <div class="relative h-48 overflow-hidden">
          <img src="${photo.thumbnail}" alt="${photo.title}" class="w-full h-full object-cover transition-transform duration-300 hover:scale-105 cursor-pointer" loading="lazy">
          <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
            <button class="photo-view-btn opacity-0 hover:opacity-100 bg-white text-gray-900 py-2 px-4 rounded-lg font-medium transition-opacity duration-300" data-photo-id="${photo.id}">
              View Full Size
            </button>
          </div>
          <div class="absolute top-4 left-4">
            <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
              ${photo.category}
            </span>
          </div>
        </div>

        <div class="p-4">
          <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">${photo.title}</h3>
          <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">${photo.description}</p>

          <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>📸 ${photo.photographer}</span>
            <span>${new Date(photo.uploadDate).toLocaleDateString()}</span>
          </div>

          <div class="flex flex-wrap gap-1 mt-3">
            ${photo.tags.slice(0, 3).map(tag => `
              <span class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs">
                ${tag}
              </span>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }

  renderTestimonials(testimonials = this.testimonials) {
    const container = document.getElementById('testimonials-grid');
    if (!container) return;

    const approvedTestimonials = testimonials.filter(t => t.status === 'approved');
    container.innerHTML = approvedTestimonials.map(testimonial => this.createTestimonialCard(testimonial)).join('');
  }

  createTestimonialCard(testimonial) {
    return `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all duration-300 ${testimonial.featured ? 'ring-2 ring-pink-500' : ''}">
        ${testimonial.featured ? '<div class="bg-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold mb-4 inline-block">Featured</div>' : ''}

        <div class="flex items-center mb-4">
          <img src="${testimonial.photo}" alt="${testimonial.name}" class="w-12 h-12 rounded-full object-cover mr-4">
          <div>
            <h3 class="text-lg font-bold text-gray-900 dark:text-white">${testimonial.name}</h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">${testimonial.role}</p>
          </div>
        </div>

        <div class="flex items-center mb-3">
          ${Array.from({length: 5}, (_, i) => `
            <svg class="h-5 w-5 ${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          `).join('')}
        </div>

        <blockquote class="text-gray-700 dark:text-gray-300 italic mb-4">
          "${testimonial.content}"
        </blockquote>

        <div class="text-xs text-gray-500 dark:text-gray-400">
          ${new Date(testimonial.submitDate).toLocaleDateString()}
        </div>
      </div>
    `;
  }

  showFullBlogPost(postId) {
    const post = this.blogPosts.find(p => p.id === postId);
    if (!post) return;

    const modal = this.createBlogPostModal(post);
    document.body.appendChild(modal);

    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
    });
  }

  createBlogPostModal(post) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden overflow-y-auto';
    modal.id = 'blog-post-modal';

    modal.innerHTML = `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-4xl w-full max-h-full overflow-y-auto">
        <div class="relative">
          <img src="${post.image}" alt="${post.title}" class="w-full h-64 object-cover">
          <button onclick="this.closest('#blog-post-modal').remove()" class="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="p-8">
          <div class="flex items-center text-sm text-gray-600 dark:text-gray-300 mb-4">
            <span class="bg-pink-600 text-white px-3 py-1 rounded-full text-xs font-medium mr-3">${post.category}</span>
            <span>${post.author}</span>
            <span class="mx-2">•</span>
            <span>${new Date(post.publishDate).toLocaleDateString()}</span>
          </div>

          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">${post.title}</h1>

          <div class="prose prose-lg dark:prose-invert max-w-none">
            ${post.content.split('\n').map(paragraph => `<p class="mb-4">${paragraph}</p>`).join('')}
          </div>

          <div class="flex flex-wrap gap-2 mt-8">
            ${post.tags.map(tag => `
              <span class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm">
                ${tag}
              </span>
            `).join('')}
          </div>

          <div class="mt-8 pt-8 border-t border-gray-200 dark:border-gray-600">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-600 dark:text-gray-300">
                Share this post:
              </div>
              <div class="flex gap-3">
                <button onclick="this.sharePost('${post.id}', 'facebook')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Share on Facebook
                </button>
                <button onclick="this.sharePost('${post.id}', 'twitter')" class="bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors">
                  Share on Twitter
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    return modal;
  }

  downloadResource(resourceId) {
    const resource = this.resources.find(r => r.id === resourceId);
    if (!resource) return;

    // Increment download count
    resource.downloadCount++;
    this.saveResources();

    // Simulate download (in real implementation, serve actual file)
    this.showNotification(`Downloading ${resource.title}...`, 'success');

    // Update UI
    this.renderResources();

    console.log('Downloading resource:', resource);
  }

  showPhotoModal(photoId) {
    const photo = this.photos.find(p => p.id === photoId);
    if (!photo) return;

    const modal = this.createPhotoModal(photo);
    document.body.appendChild(modal);

    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
    });
  }

  createPhotoModal(photo) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4 hidden';
    modal.id = 'photo-modal';

    modal.innerHTML = `
      <div class="max-w-4xl w-full">
        <div class="relative">
          <img src="${photo.url}" alt="${photo.title}" class="w-full h-auto rounded-lg">
          <button onclick="this.closest('#photo-modal').remove()" class="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="bg-white dark:bg-slate-800 rounded-b-lg p-6">
          <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">${photo.title}</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">${photo.description}</p>

          <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
            <span>📸 ${photo.photographer}</span>
            <span>${new Date(photo.uploadDate).toLocaleDateString()}</span>
          </div>

          <div class="flex flex-wrap gap-2 mt-4">
            ${photo.tags.map(tag => `
              <span class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs">
                ${tag}
              </span>
            `).join('')}
          </div>
        </div>
      </div>
    `;

    return modal;
  }

  showTestimonialForm() {
    const modal = this.createTestimonialFormModal();
    document.body.appendChild(modal);

    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
    });
  }

  createTestimonialFormModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden';
    modal.id = 'testimonial-form-modal';

    modal.innerHTML = `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-start mb-6">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Share Your Story</h3>
            <button onclick="this.closest('#testimonial-form-modal').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <form id="testimonial-form" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name *</label>
                <input type="text" name="name" required class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Role/Title</label>
                <input type="text" name="role" placeholder="e.g., Program Participant" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Your Story *</label>
              <textarea name="content" rows="5" required placeholder="Tell us about your experience with Sisters for Good..." class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white"></textarea>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Rating *</label>
              <div class="flex gap-2">
                ${Array.from({length: 5}, (_, i) => `
                  <label class="cursor-pointer">
                    <input type="radio" name="rating" value="${i + 1}" class="sr-only">
                    <svg class="h-8 w-8 text-gray-300 hover:text-yellow-400 transition-colors rating-star" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  </label>
                `).join('')}
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email (for follow-up)</label>
              <input type="email" name="email" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
            </div>

            <div class="flex items-center">
              <input type="checkbox" name="consent" required class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
              <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                I consent to sharing my testimonial on the Sisters for Good website and marketing materials.
              </label>
            </div>

            <div class="flex gap-3 pt-4">
              <button type="button" onclick="this.closest('#testimonial-form-modal').remove()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                Cancel
              </button>
              <button type="submit" class="flex-1 px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 transition-colors">
                Submit Testimonial
              </button>
            </div>
          </form>
        </div>
      </div>
    `;

    // Setup form submission
    const form = modal.querySelector('#testimonial-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleTestimonialSubmission(form);
    });

    // Setup star rating
    const stars = modal.querySelectorAll('.rating-star');
    stars.forEach((star, index) => {
      star.addEventListener('click', () => {
        stars.forEach((s, i) => {
          s.classList.toggle('text-yellow-400', i <= index);
          s.classList.toggle('text-gray-300', i > index);
        });
      });
    });

    return modal;
  }

  handleTestimonialSubmission(form) {
    const formData = new FormData(form);
    const testimonial = {
      id: this.generateId(),
      name: formData.get('name'),
      role: formData.get('role') || 'Community Member',
      content: formData.get('content'),
      rating: parseInt(formData.get('rating')) || 5,
      email: formData.get('email'),
      photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&q=80', // Default photo
      submitDate: new Date().toISOString(),
      featured: false,
      status: 'pending' // Requires approval
    };

    this.testimonials.push(testimonial);
    this.saveTestimonials();

    this.showNotification('Thank you for sharing your story! Your testimonial is pending review.', 'success');

    // Close modal
    document.getElementById('testimonial-form-modal').remove();
  }

  // Filter methods
  filterBlogPosts(searchTerm) {
    const filtered = this.blogPosts.filter(post =>
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    this.renderBlogPosts(filtered);
  }

  filterResources(searchTerm) {
    const filtered = this.resources.filter(resource =>
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    this.renderResources(filtered);
  }

  filterPhotos(searchTerm) {
    const filtered = this.photos.filter(photo =>
      photo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      photo.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      photo.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    this.renderPhotoGallery(filtered);
  }

  applyFilters() {
    // Apply category and other filters
    console.log('Applying filters...');
  }

  // Save methods
  saveBlogPosts() {
    localStorage.setItem('sisters-for-good-blog-posts', JSON.stringify(this.blogPosts));
  }

  saveResources() {
    localStorage.setItem('sisters-for-good-resources', JSON.stringify(this.resources));
  }

  savePhotos() {
    localStorage.setItem('sisters-for-good-photos', JSON.stringify(this.photos));
  }

  saveTestimonials() {
    localStorage.setItem('sisters-for-good-testimonials', JSON.stringify(this.testimonials));
  }

  // Utility methods
  showNotification(message, type = 'info') {
    // Use the accessibility system's live region if available
    if (window.announceToScreenReader) {
      window.announceToScreenReader(message, type === 'error');
    }

    // Visual notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
      type === 'success' ? 'bg-green-600 text-white' :
      type === 'error' ? 'bg-red-600 text-white' :
      type === 'warning' ? 'bg-yellow-600 text-white' :
      'bg-blue-600 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

// Initialize content management system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.contentManagement = new ContentManagementSystem();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ContentManagementSystem;
}
