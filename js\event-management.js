/**
 * Advanced Event Management System
 * Handles event registration, RSVP, calendar integration, and feedback
 */

class EventManagementSystem {
  constructor() {
    this.events = [];
    this.registrations = new Map();
    this.init();
  }

  init() {
    this.loadEvents();
    this.setupEventListeners();
    this.initializeCalendarIntegration();
  }

  loadEvents() {
    // Load events from localStorage or API
    const storedEvents = localStorage.getItem('sisters-for-good-events');
    if (storedEvents) {
      this.events = JSON.parse(storedEvents);
    } else {
      // Default events
      this.events = [
        {
          id: 'fathers-day-2025',
          title: "Father's Day Brunch",
          description: "Join us for our annual Father's Day celebration with food, fellowship, and family fun.",
          date: '2025-06-15',
          time: '10:00',
          endTime: '11:00',
          location: '247 East High Street, Pottstown, PA 19464',
          capacity: 50,
          registered: 0,
          category: 'community',
          image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&q=80',
          requiresRSVP: true,
          isRecurring: true,
          recurrencePattern: 'yearly',
          organizer: 'Sisters for Good',
          contact: '<EMAIL>',
          price: 'Free',
          tags: ['family', 'community', 'food', 'celebration']
        },
        {
          id: 'back-to-school-2025',
          title: "Back to School Hair Event",
          description: "Free haircuts and styling for children preparing for the new school year.",
          date: '2025-08-15',
          time: '09:00',
          endTime: '15:00',
          location: '247 East High Street, Pottstown, PA 19464',
          capacity: 30,
          registered: 0,
          category: 'service',
          image: 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=800&q=80',
          requiresRSVP: true,
          isRecurring: true,
          recurrencePattern: 'yearly',
          organizer: 'Sisters for Good',
          contact: '<EMAIL>',
          price: 'Free',
          tags: ['children', 'education', 'service', 'community']
        },
        {
          id: 'thanksgiving-drive-2025',
          title: "Thanksgiving Food & Coat Drive",
          description: "Help us distribute food and warm coats to families in need during the holiday season.",
          date: '2025-11-20',
          time: '14:00',
          endTime: '18:00',
          location: '247 East High Street, Pottstown, PA 19464',
          capacity: 100,
          registered: 0,
          category: 'service',
          image: 'https://images.unsplash.com/photo-1574484284002-952d92456975?w=800&q=80',
          requiresRSVP: false,
          isRecurring: true,
          recurrencePattern: 'yearly',
          organizer: 'Sisters for Good',
          contact: '<EMAIL>',
          price: 'Free',
          tags: ['thanksgiving', 'food drive', 'community service', 'families']
        }
      ];
      this.saveEvents();
    }
  }

  saveEvents() {
    localStorage.setItem('sisters-for-good-events', JSON.stringify(this.events));
  }

  setupEventListeners() {
    // RSVP form submissions
    document.addEventListener('submit', (e) => {
      if (e.target.classList.contains('rsvp-form')) {
        e.preventDefault();
        this.handleRSVP(e.target);
      }
    });

    // Event registration buttons
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('register-btn')) {
        const eventId = e.target.getAttribute('data-event-id');
        this.showRegistrationModal(eventId);
      }

      if (e.target.classList.contains('add-to-calendar-btn')) {
        const eventId = e.target.getAttribute('data-event-id');
        this.addToCalendar(eventId);
      }

      if (e.target.classList.contains('share-event-btn')) {
        const eventId = e.target.getAttribute('data-event-id');
        this.shareEvent(eventId);
      }
    });
  }

  async handleRSVP(form) {
    const formData = new FormData(form);
    const eventId = formData.get('eventId');
    const event = this.getEventById(eventId);

    if (!event) {
      this.showNotification('Event not found', 'error');
      return;
    }

    // Check capacity
    if (event.registered >= event.capacity) {
      this.showNotification('Sorry, this event is full. You can join the waitlist.', 'warning');
      this.addToWaitlist(eventId, formData);
      return;
    }

    const registration = {
      id: this.generateId(),
      eventId: eventId,
      name: formData.get('name'),
      email: formData.get('email'),
      phone: formData.get('phone'),
      guests: parseInt(formData.get('guests') || '0'),
      dietaryRestrictions: formData.get('dietaryRestrictions'),
      specialRequests: formData.get('specialRequests'),
      registrationDate: new Date().toISOString(),
      status: 'confirmed'
    };

    // Update event registration count
    event.registered += (1 + registration.guests);
    this.saveEvents();

    // Store registration
    this.registrations.set(registration.id, registration);
    this.saveRegistrations();

    // Send confirmation email
    await this.sendConfirmationEmail(registration, event);

    // Show success message
    this.showNotification('Registration successful! Check your email for confirmation.', 'success');

    // Close modal
    this.closeRegistrationModal();

    // Update UI
    this.updateEventDisplay(eventId);
  }

  showRegistrationModal(eventId) {
    const event = this.getEventById(eventId);
    if (!event) return;

    const modal = this.createRegistrationModal(event);
    document.body.appendChild(modal);

    // Show modal with animation
    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
      modal.querySelector('.modal-content').classList.add('scale-100');
    });
  }

  createRegistrationModal(event) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden';
    modal.id = 'registration-modal';

    const spotsLeft = event.capacity - event.registered;
    const isFull = spotsLeft <= 0;

    modal.innerHTML = `
      <div class="modal-content bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-md w-full max-h-96 overflow-y-auto transform scale-95 transition-transform duration-300">
        <div class="p-6">
          <div class="flex justify-between items-start mb-4">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">Register for Event</h3>
            <button onclick="this.closest('#registration-modal').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <div class="mb-4">
            <h4 class="font-semibold text-gray-900 dark:text-white">${event.title}</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">${this.formatEventDate(event)}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">${event.location}</p>
            ${!isFull ? `<p class="text-sm text-green-600 dark:text-green-400 mt-2">${spotsLeft} spots remaining</p>` : 
              `<p class="text-sm text-red-600 dark:text-red-400 mt-2">Event is full - Join waitlist</p>`}
          </div>

          <form class="rsvp-form space-y-4">
            <input type="hidden" name="eventId" value="${event.id}">
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name *</label>
              <input type="text" name="name" required class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email *</label>
              <input type="email" name="email" required class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone</label>
              <input type="tel" name="phone" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Number of Guests</label>
              <select name="guests" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
                <option value="0">Just me</option>
                <option value="1">1 guest</option>
                <option value="2">2 guests</option>
                <option value="3">3 guests</option>
                <option value="4">4+ guests</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dietary Restrictions</label>
              <textarea name="dietaryRestrictions" rows="2" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-slate-700 text-gray-900 dark:text-white" placeholder="Any food allergies or dietary needs..."></textarea>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Special Requests</label>
              <textarea name="specialRequests" rows="2" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-slate-700 text-gray-900 dark:text-white" placeholder="Any special accommodations needed..."></textarea>
            </div>

            <div class="flex gap-3 pt-4">
              <button type="button" onclick="this.closest('#registration-modal').remove()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                Cancel
              </button>
              <button type="submit" class="flex-1 px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 transition-colors">
                ${isFull ? 'Join Waitlist' : 'Register'}
              </button>
            </div>
          </form>
        </div>
      </div>
    `;

    return modal;
  }

  async sendConfirmationEmail(registration, event) {
    const emailData = {
      to: registration.email,
      subject: `Registration Confirmed: ${event.title}`,
      html: this.generateConfirmationEmail(registration, event)
    };

    try {
      // Send via Formspree or your email service
      const response = await fetch('https://formspree.io/f/your-form-id', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(emailData)
      });

      if (response.ok) {
        console.log('Confirmation email sent successfully');
      }
    } catch (error) {
      console.error('Failed to send confirmation email:', error);
    }
  }

  generateConfirmationEmail(registration, event) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #fc5c7d, #6a82fb); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">Registration Confirmed!</h1>
        </div>
        
        <div style="padding: 20px; background: #f9f9f9;">
          <h2 style="color: #333;">Event Details</h2>
          <p><strong>Event:</strong> ${event.title}</p>
          <p><strong>Date:</strong> ${this.formatEventDate(event)}</p>
          <p><strong>Location:</strong> ${event.location}</p>
          <p><strong>Attendees:</strong> ${registration.name} ${registration.guests > 0 ? `+ ${registration.guests} guests` : ''}</p>
          
          <div style="margin: 20px 0; padding: 15px; background: white; border-radius: 5px;">
            <h3 style="color: #fc5c7d; margin-top: 0;">What to Expect</h3>
            <p>${event.description}</p>
          </div>
          
          <div style="text-align: center; margin: 20px 0;">
            <a href="#" style="background: #fc5c7d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Add to Calendar</a>
          </div>
          
          <p style="font-size: 12px; color: #666;">
            If you need to cancel or modify your registration, please contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    `;
  }

  addToCalendar(eventId) {
    const event = this.getEventById(eventId);
    if (!event) return;

    const startDate = new Date(`${event.date}T${event.time}`);
    const endDate = new Date(`${event.date}T${event.endTime}`);

    // Google Calendar URL
    const googleCalendarUrl = this.generateGoogleCalendarUrl(event, startDate, endDate);
    
    // Show calendar options modal
    this.showCalendarOptionsModal(event, googleCalendarUrl);
  }

  generateGoogleCalendarUrl(event, startDate, endDate) {
    const formatDate = (date) => date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    
    const params = new URLSearchParams({
      action: 'TEMPLATE',
      text: event.title,
      dates: `${formatDate(startDate)}/${formatDate(endDate)}`,
      details: event.description,
      location: event.location,
      sprop: 'website:sistersforgood.org'
    });

    return `https://calendar.google.com/calendar/render?${params.toString()}`;
  }

  showCalendarOptionsModal(event, googleCalendarUrl) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4';
    modal.innerHTML = `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-sm w-full p-6">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Add to Calendar</h3>
        <div class="space-y-3">
          <a href="${googleCalendarUrl}" target="_blank" class="block w-full p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
            Google Calendar
          </a>
          <button onclick="this.downloadICS('${event.id}')" class="block w-full p-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            Download .ics file
          </button>
          <button onclick="this.closest('div').remove()" class="block w-full p-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
            Cancel
          </button>
        </div>
      </div>
    `;
    document.body.appendChild(modal);
  }

  // Helper methods
  getEventById(id) {
    return this.events.find(event => event.id === id);
  }

  formatEventDate(event) {
    const date = new Date(event.date);
    const time = event.time;
    return `${date.toLocaleDateString()} at ${this.formatTime(time)}`;
  }

  formatTime(time) {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  saveRegistrations() {
    const registrationsArray = Array.from(this.registrations.entries());
    localStorage.setItem('sisters-for-good-registrations', JSON.stringify(registrationsArray));
  }

  showNotification(message, type = 'info') {
    // Use the accessibility system's live region if available
    if (window.announceToScreenReader) {
      window.announceToScreenReader(message, type === 'error');
    }

    // Visual notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
      type === 'success' ? 'bg-green-600 text-white' :
      type === 'error' ? 'bg-red-600 text-white' :
      type === 'warning' ? 'bg-yellow-600 text-white' :
      'bg-blue-600 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  closeRegistrationModal() {
    const modal = document.getElementById('registration-modal');
    if (modal) {
      modal.remove();
    }
  }

  updateEventDisplay(eventId) {
    // Update event cards with new registration count
    const eventCards = document.querySelectorAll(`[data-event-id="${eventId}"]`);
    const event = this.getEventById(eventId);
    
    eventCards.forEach(card => {
      const spotsElement = card.querySelector('.spots-remaining');
      if (spotsElement && event) {
        const spotsLeft = event.capacity - event.registered;
        spotsElement.textContent = spotsLeft > 0 ? `${spotsLeft} spots remaining` : 'Event full';
        spotsElement.className = spotsLeft > 0 ? 'spots-remaining text-green-600' : 'spots-remaining text-red-600';
      }
    });
  }

  initializeCalendarIntegration() {
    // Initialize any calendar-specific functionality
    console.log('Calendar integration initialized');
  }
}

// Initialize event management system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.eventManagement = new EventManagementSystem();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EventManagementSystem;
}
