{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Alter Dire"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\sfg_2025.jpg not found.\n", "File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\bitcoin-qr.png not found.\n", "File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\ethereum-qr.png not found.\n", "File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\paypal-barcode.png not found.\n", "File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\thanksgiving-event.jpg not found.\n", "File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\styles.css not found.\n", "File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\script.js not found.\n", "Moved c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\README.md to c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\docs\\README.md\n", "File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\LICENSE not found.\n", "File c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\CONTRIBUTING.md not found.\n", "Created placeholder file: c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\README.md\n", "Created placeholder file: c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\LICENSE\n", "Created placeholder file: c:\\Users\\<USER>\\Documents\\dev\\business\\sistersforgood\\sisters_for_good\\CONTRIBUTING.md\n", "Created .gitignore file with common ignores.\n", "Directory structure organized successfully!\n"]}], "source": ["import os\n", "import shutil\n", "\n", "# Define the target directory structure\n", "structure = {\n", "    \"assets\": [\"sfg_2025.jpg\", \"bitcoin-qr.png\", \"ethereum-qr.png\", \"paypal-barcode.png\", \"thanksgiving-event.jpg\"],\n", "    \"css\": [\"styles.css\"],\n", "    \"js\": [\"script.js\"],\n", "    \"fonts\": [],\n", "    \"docs\": [\"README.md\", \"LICENSE\", \"CONTRIBUTING.md\"],\n", "}\n", "\n", "# Define the current file names (replace these with your current files if different)\n", "current_files = {\n", "    \"sfg_2025.jpg\": \"assets/sfg_2025.jpg\",\n", "    \"bitcoin-qr.png\": \"assets/bitcoin-qr.png\",\n", "    \"ethereum-qr.png\": \"assets/ethereum-qr.png\",\n", "    \"paypal-barcode.png\": \"assets/paypal-barcode.png\",\n", "    \"thanksgiving-event.jpg\": \"assets/thanksgiving-event.jpg\",\n", "    \"styles.css\": \"css/styles.css\",\n", "    \"index.html\": \"index.html\",\n", "    \"whois.py\": \"whois.py\"\n", "}\n", "\n", "# Define the project root path\n", "project_root = os.getcwd()\n", "\n", "def create_directory(path):\n", "    \"\"\"Create a directory if it doesn't exist\"\"\"\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "def move_file(file, destination):\n", "    \"\"\"Move a file to the new directory\"\"\"\n", "    try:\n", "        shutil.move(file, destination)\n", "        print(f\"Moved {file} to {destination}\")\n", "    except FileNotFoundError:\n", "        print(f\"File {file} not found.\")\n", "    except Exception as e:\n", "        print(f\"Error moving {file}: {e}\")\n", "\n", "def create_placeholder_file(path):\n", "    \"\"\"Create an empty placeholder file (if not already present)\"\"\"\n", "    if not os.path.exists(path):\n", "        with open(path, 'w') as f:\n", "            pass\n", "        print(f\"Created placeholder file: {path}\")\n", "\n", "def setup_directory_structure():\n", "    \"\"\"Setup the directory structure according to the defined structure\"\"\"\n", "    \n", "    # Create folders and move the files\n", "    for folder, files in structure.items():\n", "        folder_path = os.path.join(project_root, folder)\n", "        create_directory(folder_path)\n", "        \n", "        for file in files:\n", "            file_path = os.path.join(project_root, file)\n", "            move_file(file_path, os.path.join(folder_path, file))\n", "    \n", "    # Create necessary placeholder files (e.g., README.md, LICENSE)\n", "    create_placeholder_file(os.path.join(project_root, \"README.md\"))\n", "    create_placeholder_file(os.path.join(project_root, \"LICENSE\"))\n", "    create_placeholder_file(os.path.join(project_root, \"CONTRIBUTING.md\"))\n", "    \n", "    # Create .gitignore with common ignores\n", "    gitignore_path = os.path.join(project_root, \".gitignore\")\n", "    if not os.path.exists(gitignore_path):\n", "        with open(gitignore_path, 'w') as f:\n", "            f.write(\"node_modules/\\n*.log\\n*.env\\n\")\n", "        print(f\"Created .gitignore file with common ignores.\")\n", "\n", "def main():\n", "    \"\"\"Main function to reorganize the directory\"\"\"\n", "    setup_directory_structure()\n", "    print(\"Directory structure organized successfully!\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}