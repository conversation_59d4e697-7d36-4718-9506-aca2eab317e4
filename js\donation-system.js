/**
 * Enhanced Donation System
 * Handles recurring donations, impact calculator, donor recognition, and tax receipts
 */

class EnhancedDonationSystem {
  constructor() {
    this.donations = new Map();
    this.donors = new Map();
    this.impactMetrics = {
      cosmetologyStudents: { cost: 500, description: "Support one cosmetology student for a month" },
      familyMeals: { cost: 25, description: "Provide meals for a family during events" },
      backToSchoolKit: { cost: 50, description: "Complete back-to-school kit for one child" },
      workshopMaterials: { cost: 100, description: "Materials for one educational workshop" },
      communityEvent: { cost: 200, description: "Host one community gathering" }
    };
    this.init();
  }

  init() {
    this.loadDonationData();
    this.setupEventListeners();
    this.initializePaymentProcessing();
    this.setupRecurringDonations();
  }

  loadDonationData() {
    // Load existing donation data from localStorage
    const storedDonations = localStorage.getItem('sisters-for-good-donations');
    const storedDonors = localStorage.getItem('sisters-for-good-donors');
    
    if (storedDonations) {
      const donationsArray = JSON.parse(storedDonations);
      this.donations = new Map(donationsArray);
    }
    
    if (storedDonors) {
      const donorsArray = JSON.parse(storedDonors);
      this.donors = new Map(donorsArray);
    }
  }

  setupEventListeners() {
    // Donation form submissions
    document.addEventListener('submit', (e) => {
      if (e.target.classList.contains('donation-form')) {
        e.preventDefault();
        this.handleDonation(e.target);
      }
    });

    // Quick donation buttons
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('quick-donate-btn')) {
        const amount = e.target.getAttribute('data-amount');
        this.setDonationAmount(amount);
      }

      if (e.target.classList.contains('impact-calculator-btn')) {
        this.showImpactCalculator();
      }

      if (e.target.classList.contains('donor-wall-btn')) {
        this.showDonorWall();
      }

      if (e.target.classList.contains('tax-receipt-btn')) {
        const donationId = e.target.getAttribute('data-donation-id');
        this.generateTaxReceipt(donationId);
      }
    });

    // Amount input changes for impact calculation
    document.addEventListener('input', (e) => {
      if (e.target.id === 'donation-amount' || e.target.classList.contains('donation-amount-input')) {
        this.updateImpactDisplay(e.target.value);
      }
    });

    // Recurring donation toggle
    document.addEventListener('change', (e) => {
      if (e.target.id === 'recurring-donation') {
        this.toggleRecurringOptions(e.target.checked);
      }
    });
  }

  async handleDonation(form) {
    const formData = new FormData(form);
    const donationData = {
      id: this.generateId(),
      amount: parseFloat(formData.get('amount')),
      frequency: formData.get('frequency') || 'one-time',
      donorName: formData.get('donorName'),
      email: formData.get('email'),
      phone: formData.get('phone'),
      address: formData.get('address'),
      city: formData.get('city'),
      state: formData.get('state'),
      zipCode: formData.get('zipCode'),
      anonymous: formData.get('anonymous') === 'on',
      dedication: formData.get('dedication'),
      dedicationMessage: formData.get('dedicationMessage'),
      paymentMethod: formData.get('paymentMethod'),
      timestamp: new Date().toISOString(),
      status: 'pending'
    };

    try {
      // Show processing state
      this.showProcessingState(form);

      // Process payment (integration with payment processor)
      const paymentResult = await this.processPayment(donationData);
      
      if (paymentResult.success) {
        donationData.status = 'completed';
        donationData.transactionId = paymentResult.transactionId;
        
        // Store donation
        this.donations.set(donationData.id, donationData);
        this.saveDonationData();

        // Update or create donor profile
        this.updateDonorProfile(donationData);

        // Setup recurring donation if applicable
        if (donationData.frequency !== 'one-time') {
          this.setupRecurringDonation(donationData);
        }

        // Send confirmation email
        await this.sendDonationConfirmation(donationData);

        // Generate tax receipt
        this.generateTaxReceipt(donationData.id);

        // Show success message
        this.showDonationSuccess(donationData);

        // Update impact display
        this.updateTotalImpact();

      } else {
        throw new Error(paymentResult.error || 'Payment processing failed');
      }

    } catch (error) {
      console.error('Donation processing error:', error);
      this.showDonationError(error.message);
    }
  }

  async processPayment(donationData) {
    // Simulate payment processing (integrate with Stripe, PayPal, etc.)
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate successful payment
        resolve({
          success: true,
          transactionId: 'txn_' + this.generateId(),
          processorResponse: 'Payment processed successfully'
        });
      }, 2000);
    });
  }

  updateDonorProfile(donationData) {
    const donorKey = donationData.email.toLowerCase();
    let donor = this.donors.get(donorKey);

    if (!donor) {
      donor = {
        id: this.generateId(),
        name: donationData.donorName,
        email: donationData.email,
        phone: donationData.phone,
        address: {
          street: donationData.address,
          city: donationData.city,
          state: donationData.state,
          zipCode: donationData.zipCode
        },
        totalDonated: 0,
        donationCount: 0,
        firstDonation: donationData.timestamp,
        lastDonation: donationData.timestamp,
        isRecurring: donationData.frequency !== 'one-time',
        anonymous: donationData.anonymous,
        donationHistory: []
      };
    }

    // Update donor statistics
    donor.totalDonated += donationData.amount;
    donor.donationCount += 1;
    donor.lastDonation = donationData.timestamp;
    donor.donationHistory.push(donationData.id);

    // Update donor tier
    donor.tier = this.calculateDonorTier(donor.totalDonated);

    this.donors.set(donorKey, donor);
    this.saveDonorData();
  }

  calculateDonorTier(totalDonated) {
    if (totalDonated >= 5000) return 'platinum';
    if (totalDonated >= 1000) return 'gold';
    if (totalDonated >= 500) return 'silver';
    if (totalDonated >= 100) return 'bronze';
    return 'supporter';
  }

  setupRecurringDonation(donationData) {
    const recurringDonation = {
      id: this.generateId(),
      originalDonationId: donationData.id,
      donorEmail: donationData.email,
      amount: donationData.amount,
      frequency: donationData.frequency,
      nextProcessingDate: this.calculateNextProcessingDate(donationData.frequency),
      isActive: true,
      createdAt: donationData.timestamp
    };

    // Store recurring donation info
    const recurringDonations = JSON.parse(localStorage.getItem('recurring-donations') || '[]');
    recurringDonations.push(recurringDonation);
    localStorage.setItem('recurring-donations', JSON.stringify(recurringDonations));

    console.log('Recurring donation setup:', recurringDonation);
  }

  calculateNextProcessingDate(frequency) {
    const now = new Date();
    switch (frequency) {
      case 'monthly':
        return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
      case 'quarterly':
        return new Date(now.getFullYear(), now.getMonth() + 3, now.getDate());
      case 'annually':
        return new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
      default:
        return null;
    }
  }

  updateImpactDisplay(amount) {
    const impactContainer = document.getElementById('impact-display');
    if (!impactContainer || !amount) return;

    const donationAmount = parseFloat(amount);
    if (isNaN(donationAmount) || donationAmount <= 0) {
      impactContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">Enter an amount to see your impact</p>';
      return;
    }

    const impacts = this.calculateImpact(donationAmount);
    impactContainer.innerHTML = this.generateImpactHTML(impacts);
  }

  calculateImpact(amount) {
    const impacts = [];
    
    Object.entries(this.impactMetrics).forEach(([key, metric]) => {
      const count = Math.floor(amount / metric.cost);
      if (count > 0) {
        impacts.push({
          type: key,
          count: count,
          description: metric.description,
          icon: this.getImpactIcon(key)
        });
      }
    });

    return impacts.slice(0, 3); // Show top 3 impacts
  }

  generateImpactHTML(impacts) {
    if (impacts.length === 0) {
      return '<p class="text-gray-500 dark:text-gray-400">Every donation makes a difference!</p>';
    }

    return `
      <div class="space-y-3">
        <h4 class="font-semibold text-gray-900 dark:text-white mb-3">Your Impact:</h4>
        ${impacts.map(impact => `
          <div class="flex items-center p-3 bg-pink-50 dark:bg-pink-900/20 rounded-lg">
            <span class="text-2xl mr-3">${impact.icon}</span>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">
                ${impact.count} × ${impact.description}
              </p>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  getImpactIcon(type) {
    const icons = {
      cosmetologyStudents: '💇‍♀️',
      familyMeals: '🍽️',
      backToSchoolKit: '🎒',
      workshopMaterials: '📚',
      communityEvent: '🤝'
    };
    return icons[type] || '💝';
  }

  showImpactCalculator() {
    const modal = this.createImpactCalculatorModal();
    document.body.appendChild(modal);
    
    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
    });
  }

  createImpactCalculatorModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden';
    modal.id = 'impact-calculator-modal';

    modal.innerHTML = `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-start mb-6">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Donation Impact Calculator</h3>
            <button onclick="this.closest('#impact-calculator-modal').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Donation Amount</label>
            <div class="relative">
              <span class="absolute left-3 top-3 text-gray-500">$</span>
              <input type="number" id="calculator-amount" min="1" step="1" placeholder="100" 
                     class="w-full pl-8 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white text-lg">
            </div>
          </div>

          <div id="calculator-impact-display" class="mb-6">
            <p class="text-gray-500 dark:text-gray-400">Enter an amount to see your potential impact</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            ${Object.entries(this.impactMetrics).map(([key, metric]) => `
              <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div class="flex items-center mb-2">
                  <span class="text-2xl mr-2">${this.getImpactIcon(key)}</span>
                  <span class="font-medium text-gray-900 dark:text-white">$${metric.cost}</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-300">${metric.description}</p>
              </div>
            `).join('')}
          </div>

          <div class="flex gap-3">
            <button onclick="this.closest('#impact-calculator-modal').remove()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
              Close
            </button>
            <button onclick="this.donateFromCalculator()" class="flex-1 px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 transition-colors">
              Donate This Amount
            </button>
          </div>
        </div>
      </div>
    `;

    // Setup calculator input listener
    const calculatorInput = modal.querySelector('#calculator-amount');
    calculatorInput.addEventListener('input', (e) => {
      const amount = parseFloat(e.target.value);
      const impacts = this.calculateImpact(amount);
      const displayElement = modal.querySelector('#calculator-impact-display');
      displayElement.innerHTML = this.generateImpactHTML(impacts);
    });

    return modal;
  }

  async sendDonationConfirmation(donationData) {
    const emailData = {
      to: donationData.email,
      subject: 'Thank You for Your Donation - Sisters for Good',
      html: this.generateDonationConfirmationEmail(donationData)
    };

    try {
      // Send via email service (Formspree, SendGrid, etc.)
      console.log('Sending donation confirmation email:', emailData);
      // In real implementation, integrate with email service
    } catch (error) {
      console.error('Failed to send donation confirmation:', error);
    }
  }

  generateDonationConfirmationEmail(donationData) {
    const impacts = this.calculateImpact(donationData.amount);
    
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #fc5c7d, #6a82fb); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Thank You!</h1>
          <p style="color: white; margin: 10px 0 0 0; font-size: 18px;">Your generosity makes a difference</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333; margin-top: 0;">Donation Confirmation</h2>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #fc5c7d; margin-top: 0;">Donation Details</h3>
            <p><strong>Amount:</strong> $${donationData.amount.toFixed(2)}</p>
            <p><strong>Frequency:</strong> ${donationData.frequency}</p>
            <p><strong>Transaction ID:</strong> ${donationData.transactionId}</p>
            <p><strong>Date:</strong> ${new Date(donationData.timestamp).toLocaleDateString()}</p>
          </div>

          ${impacts.length > 0 ? `
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #fc5c7d; margin-top: 0;">Your Impact</h3>
              ${impacts.map(impact => `
                <p style="margin: 10px 0;">
                  ${impact.icon} <strong>${impact.count}</strong> × ${impact.description}
                </p>
              `).join('')}
            </div>
          ` : ''}

          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #fc5c7d; margin-top: 0;">Tax Information</h3>
            <p>Sisters for Good is a 501(c)(3) nonprofit organization. Your donation is tax-deductible to the full extent allowed by law.</p>
            <p><strong>Tax ID:</strong> XX-XXXXXXX</p>
            <p>Please keep this email as your tax receipt.</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <p style="font-size: 16px; color: #666;">
              Thank you for supporting our mission to empower women and strengthen communities!
            </p>
          </div>
        </div>
      </div>
    `;
  }

  generateTaxReceipt(donationId) {
    const donation = this.donations.get(donationId);
    if (!donation) return;

    const receiptContent = this.generateTaxReceiptContent(donation);
    
    // Create downloadable PDF (in real implementation, use PDF library)
    const blob = new Blob([receiptContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tax-receipt-${donation.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    this.showNotification('Tax receipt downloaded successfully!', 'success');
  }

  generateTaxReceiptContent(donation) {
    return `
TAX RECEIPT - SISTERS FOR GOOD

Donation Receipt for Tax Purposes

Donor Information:
Name: ${donation.donorName}
Email: ${donation.email}
Address: ${donation.address}, ${donation.city}, ${donation.state} ${donation.zipCode}

Donation Details:
Amount: $${donation.amount.toFixed(2)}
Date: ${new Date(donation.timestamp).toLocaleDateString()}
Transaction ID: ${donation.transactionId}
Payment Method: ${donation.paymentMethod}

Organization Information:
Sisters for Good
247 East High Street
Pottstown, PA 19464
Tax ID: XX-XXXXXXX

This organization is exempt from federal income tax under section 501(c)(3) of the Internal Revenue Code.

No goods or services were provided in exchange for this donation.

Thank you for your generous support!
    `;
  }

  // Helper methods
  setDonationAmount(amount) {
    const amountInput = document.getElementById('donation-amount');
    if (amountInput) {
      amountInput.value = amount;
      this.updateImpactDisplay(amount);
    }
  }

  toggleRecurringOptions(isRecurring) {
    const recurringOptions = document.getElementById('recurring-options');
    if (recurringOptions) {
      recurringOptions.style.display = isRecurring ? 'block' : 'none';
    }
  }

  showProcessingState(form) {
    const submitButton = form.querySelector('button[type="submit"]');
    if (submitButton) {
      submitButton.disabled = true;
      submitButton.innerHTML = 'Processing... <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>';
    }
  }

  showDonationSuccess(donationData) {
    this.showNotification(`Thank you for your $${donationData.amount} donation!`, 'success');
    
    // Reset form
    const form = document.querySelector('.donation-form');
    if (form) {
      form.reset();
      const submitButton = form.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.disabled = false;
        submitButton.innerHTML = 'Donate Now';
      }
    }
  }

  showDonationError(message) {
    this.showNotification(`Donation failed: ${message}`, 'error');
    
    // Re-enable form
    const form = document.querySelector('.donation-form');
    if (form) {
      const submitButton = form.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.disabled = false;
        submitButton.innerHTML = 'Donate Now';
      }
    }
  }

  showNotification(message, type = 'info') {
    // Use the accessibility system's live region if available
    if (window.announceToScreenReader) {
      window.announceToScreenReader(message, type === 'error');
    }

    // Visual notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
      type === 'success' ? 'bg-green-600 text-white' :
      type === 'error' ? 'bg-red-600 text-white' :
      type === 'warning' ? 'bg-yellow-600 text-white' :
      'bg-blue-600 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  updateTotalImpact() {
    // Update total impact statistics on the page
    const totalDonated = Array.from(this.donations.values())
      .filter(d => d.status === 'completed')
      .reduce((sum, d) => sum + d.amount, 0);

    const impactElements = document.querySelectorAll('.total-impact-amount');
    impactElements.forEach(el => {
      el.textContent = `$${totalDonated.toLocaleString()}`;
    });
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  saveDonationData() {
    const donationsArray = Array.from(this.donations.entries());
    localStorage.setItem('sisters-for-good-donations', JSON.stringify(donationsArray));
  }

  saveDonorData() {
    const donorsArray = Array.from(this.donors.entries());
    localStorage.setItem('sisters-for-good-donors', JSON.stringify(donorsArray));
  }

  initializePaymentProcessing() {
    // Initialize payment processor (Stripe, PayPal, etc.)
    console.log('Payment processing initialized');
  }

  setupRecurringDonations() {
    // Setup recurring donation processing
    console.log('Recurring donations system initialized');
  }
}

// Initialize donation system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.donationSystem = new EnhancedDonationSystem();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedDonationSystem;
}
