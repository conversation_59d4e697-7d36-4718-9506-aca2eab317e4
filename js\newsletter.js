/**
 * Sisters for Good - Newsletter Signup
 * Handles newsletter subscription functionality
 */

document.addEventListener('DOMContentLoaded', function() {
  const newsletterForm = document.getElementById('newsletter-form');
  const emailInput = document.getElementById('newsletter-email');
  const statusMessage = document.getElementById('newsletter-message');
  
  if (newsletterForm && emailInput && statusMessage) {
    newsletterForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const email = emailInput.value.trim();
      
      // Validate email
      if (!isValidEmail(email)) {
        showStatus('Please enter a valid email address', 'text-red-600');
        return;
      }
      
      // Show loading state
      showStatus('Subscribing...', 'text-blue-600');
      
      try {
        // Simulate API call (in a real implementation, this would be an actual API call)
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Simulate successful subscription
        showStatus('Thank you for subscribing to our newsletter!', 'text-green-600');
        emailInput.value = '';
        
        // In a real implementation, you would send the data to a server:
        /*
        const response = await fetch('/api/subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
        });
        
        if (response.ok) {
          showStatus('Thank you for subscribing to our newsletter!', 'text-green-600');
          emailInput.value = '';
        } else {
          const errorData = await response.json();
          showStatus(`Error: ${errorData.message || 'Something went wrong'}`, 'text-red-600');
        }
        */
      } catch (error) {
        console.error('Error subscribing to newsletter:', error);
        showStatus('An error occurred. Please try again later.', 'text-red-600');
      }
    });
  }
  
  // Show status message
  function showStatus(message, className) {
    statusMessage.textContent = message;
    statusMessage.className = `mt-2 text-sm ${className}`;
  }
  
  // Validate email format
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
});
