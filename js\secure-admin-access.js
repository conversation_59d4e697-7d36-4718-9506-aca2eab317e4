/**
 * Secure Admin Access System
 * Invisible password-based admin activation for Sisters for Good website
 */

class SecureAdminAccess {
  constructor() {
    this.adminPassword = '9733459376';
    this.keySequence = '';
    this.maxSequenceLength = 15; // Prevent memory buildup
    this.isAdminMode = false;
    this.targetPage = 'contact.html';
    this.sequenceTimeout = null;
    this.sequenceTimeoutDuration = 5000; // Reset sequence after 5 seconds of inactivity
    this.maxAttempts = 5;
    this.attemptCount = 0;
    this.lockoutTime = 300000; // 5 minutes lockout after max attempts
    this.lastAttemptTime = 0;
    this.init();
  }

  init() {
    // Only activate on contact page
    if (!this.isTargetPage()) {
      return;
    }

    // Check if admin mode is already active
    this.checkExistingAdminMode();

    // Setup keypress listener
    this.setupPasswordListener();

    // Setup session persistence
    this.setupSessionPersistence();

    console.log('🔐 Secure admin access system initialized for contact page');
  }

  isTargetPage() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    return currentPage === this.targetPage;
  }

  checkExistingAdminMode() {
    // Check URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const urlAdmin = urlParams.get('admin') === 'true';

    // Check localStorage
    const storageAdmin = localStorage.getItem('sfg-admin-access') === 'true';

    // Check sessionStorage for current session
    const sessionAdmin = sessionStorage.getItem('sfg-admin-session') === 'true';

    if (urlAdmin || storageAdmin || sessionAdmin) {
      this.activateAdminMode(false); // Don't show notification for existing admin
    }
  }

  setupPasswordListener() {
    // Global keypress listener that works regardless of focus
    document.addEventListener('keydown', (event) => {
      this.handleKeyPress(event);
    }, true); // Use capture phase to catch all events

    // Also listen for keypress events to catch different scenarios
    document.addEventListener('keypress', (event) => {
      this.handleKeyPress(event);
    }, true);

    // Listen for input events in case of special keyboards
    document.addEventListener('input', (event) => {
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        // For input fields, check the current value
        this.checkPasswordInInput(event.target.value);
      }
    });
  }

  handleKeyPress(event) {
    // Skip if admin mode is already active
    if (this.isAdminMode) {
      return;
    }

    // Get the key that was pressed
    let key = '';

    if (event.key && event.key.length === 1) {
      key = event.key;
    } else if (event.keyCode >= 48 && event.keyCode <= 57) {
      // Number keys
      key = String.fromCharCode(event.keyCode);
    } else if (event.keyCode >= 96 && event.keyCode <= 105) {
      // Numpad keys
      key = String.fromCharCode(event.keyCode - 48);
    }

    if (key && /[0-9]/.test(key)) {
      this.addToSequence(key);
    } else if (event.key === 'Escape' || event.key === 'Enter') {
      // Reset sequence on escape or enter
      this.resetSequence();
    }
  }

  addToSequence(key) {
    // Check if in lockout period
    if (this.isInLockout()) {
      return;
    }

    this.keySequence += key;

    // Reset sequence timeout
    this.resetSequenceTimeout();

    // Limit sequence length to prevent memory issues
    if (this.keySequence.length > this.maxSequenceLength) {
      this.keySequence = this.keySequence.slice(-this.maxSequenceLength);
    }

    // Check if password is contained in the sequence
    if (this.keySequence.includes(this.adminPassword)) {
      this.activateAdminMode(true);
      this.resetSequence();
      this.resetAttemptCount();
    } else if (this.keySequence.length >= this.adminPassword.length) {
      // Potential failed attempt
      this.handleFailedAttempt();
    }

    // Debug logging (remove in production)
    console.log('🔑 Key sequence:', this.keySequence.replace(/./g, '*'));
  }

  resetSequenceTimeout() {
    if (this.sequenceTimeout) {
      clearTimeout(this.sequenceTimeout);
    }

    this.sequenceTimeout = setTimeout(() => {
      this.resetSequence();
    }, this.sequenceTimeoutDuration);
  }

  isInLockout() {
    if (this.attemptCount >= this.maxAttempts) {
      const timeSinceLastAttempt = Date.now() - this.lastAttemptTime;
      if (timeSinceLastAttempt < this.lockoutTime) {
        return true;
      } else {
        // Lockout period expired, reset attempts
        this.resetAttemptCount();
      }
    }
    return false;
  }

  handleFailedAttempt() {
    this.attemptCount++;
    this.lastAttemptTime = Date.now();

    if (this.attemptCount >= this.maxAttempts) {
      this.showLockoutNotification();
      console.warn('🚫 Admin access locked due to multiple failed attempts');
    }

    this.resetSequence();
  }

  resetAttemptCount() {
    this.attemptCount = 0;
    this.lastAttemptTime = 0;
  }

  showLockoutNotification() {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 z-50 bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300';
    notification.innerHTML = `
      <div class="flex items-center">
        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
        <span class="font-medium">Access Temporarily Locked</span>
      </div>
    `;

    document.body.appendChild(notification);

    requestAnimationFrame(() => {
      notification.classList.remove('translate-x-full');
    });

    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 5000);
  }

  checkPasswordInInput(inputValue) {
    // Check if password appears in input field value
    if (inputValue && inputValue.includes(this.adminPassword)) {
      this.activateAdminMode(true);
    }
  }

  resetSequence() {
    this.keySequence = '';
  }

  activateAdminMode(showNotification = true) {
    if (this.isAdminMode) {
      return; // Already in admin mode
    }

    this.isAdminMode = true;

    // Set admin access flags
    localStorage.setItem('sfg-admin-access', 'true');
    sessionStorage.setItem('sfg-admin-session', 'true');

    // Show confirmation notification
    if (showNotification) {
      this.showAdminActivationNotification();
    }

    // Activate admin features
    this.enableAdminFeatures();

    // Log activation (remove in production)
    console.log('🔓 Admin mode activated');
  }

  showAdminActivationNotification() {
    // Create subtle notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 z-50 bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300';
    notification.innerHTML = `
      <div class="flex items-center">
        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Admin Access Activated</span>
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    requestAnimationFrame(() => {
      notification.classList.remove('translate-x-full');
    });

    // Auto-hide after 3 seconds
    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  }

  enableAdminFeatures() {
    // Trigger admin mode in existing systems
    this.activateAnalyticsDashboard();
    this.activateSEORecommendations();
    this.activatePerformanceMonitoring();

    // Add admin indicator to page
    this.addAdminIndicator();
  }

  activateAnalyticsDashboard() {
    // Check if analytics system exists and create dashboard
    if (window.analyticsSystem && typeof window.analyticsSystem.createAdminDashboard === 'function') {
      window.analyticsSystem.createAdminDashboard();
    } else {
      // Wait for analytics system to load
      const checkAnalytics = setInterval(() => {
        if (window.analyticsSystem && typeof window.analyticsSystem.createAdminDashboard === 'function') {
          window.analyticsSystem.createAdminDashboard();
          clearInterval(checkAnalytics);
        }
      }, 100);

      // Stop checking after 5 seconds
      setTimeout(() => clearInterval(checkAnalytics), 5000);
    }
  }

  activateSEORecommendations() {
    // Trigger SEO analysis if system exists
    if (window.seoMonitoring && typeof window.seoMonitoring.analyzePageSEO === 'function') {
      window.seoMonitoring.analyzePageSEO();
    } else {
      // Wait for SEO system to load
      const checkSEO = setInterval(() => {
        if (window.seoMonitoring && typeof window.seoMonitoring.analyzePageSEO === 'function') {
          window.seoMonitoring.analyzePageSEO();
          clearInterval(checkSEO);
        }
      }, 100);

      setTimeout(() => clearInterval(checkSEO), 5000);
    }
  }

  activatePerformanceMonitoring() {
    // Enable enhanced performance monitoring
    if (window.performanceOptimizer) {
      console.log('📊 Enhanced performance monitoring activated');
    }
  }

  addAdminIndicator() {
    // Add subtle admin mode indicator
    const indicator = document.createElement('div');
    indicator.id = 'admin-mode-indicator';
    indicator.className = 'fixed top-4 left-4 z-40 bg-red-600 text-white px-3 py-1 rounded-full text-xs font-bold opacity-75 hover:opacity-100 transition-opacity';
    indicator.innerHTML = '🔧 ADMIN';
    indicator.title = 'Admin mode is active';

    document.body.appendChild(indicator);

    // Add click handler to deactivate admin mode
    indicator.addEventListener('click', () => {
      this.deactivateAdminMode();
    });
  }

  deactivateAdminMode() {
    this.isAdminMode = false;

    // Clear admin access flags
    localStorage.removeItem('sfg-admin-access');
    sessionStorage.removeItem('sfg-admin-session');

    // Remove admin elements
    const indicator = document.getElementById('admin-mode-indicator');
    if (indicator) {
      indicator.remove();
    }

    const dashboardBtn = document.getElementById('analytics-dashboard-btn');
    if (dashboardBtn) {
      dashboardBtn.remove();
    }

    // Show deactivation notification
    this.showDeactivationNotification();

    console.log('🔒 Admin mode deactivated');
  }

  showDeactivationNotification() {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 z-50 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300';
    notification.innerHTML = `
      <div class="flex items-center">
        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
        <span class="font-medium">Admin Access Deactivated</span>
      </div>
    `;

    document.body.appendChild(notification);

    requestAnimationFrame(() => {
      notification.classList.remove('translate-x-full');
    });

    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  }

  setupSessionPersistence() {
    // Check session storage on page load
    window.addEventListener('beforeunload', () => {
      if (this.isAdminMode) {
        sessionStorage.setItem('sfg-admin-session', 'true');
      }
    });

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.isAdminMode) {
        sessionStorage.setItem('sfg-admin-session', 'true');
      }
    });
  }

  // Public method to check admin status
  isAdmin() {
    return this.isAdminMode ||
           localStorage.getItem('sfg-admin-access') === 'true' ||
           sessionStorage.getItem('sfg-admin-session') === 'true';
  }

  // Public method for manual activation (for testing)
  manualActivation() {
    this.activateAdminMode(true);
  }

  // Security method to clear all traces
  clearAdminAccess() {
    this.isAdminMode = false;
    this.keySequence = '';
    localStorage.removeItem('sfg-admin-access');
    sessionStorage.removeItem('sfg-admin-session');

    // Remove all admin elements
    const elements = [
      'admin-mode-indicator',
      'analytics-dashboard-btn',
      'seo-recommendations-panel'
    ];

    elements.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.remove();
      }
    });
  }
}

// Initialize secure admin access system
document.addEventListener('DOMContentLoaded', () => {
  window.secureAdminAccess = new SecureAdminAccess();
});

// Expose admin check function globally for other scripts
window.isAdminUser = function() {
  if (window.secureAdminAccess) {
    return window.secureAdminAccess.isAdmin();
  }

  // Fallback to existing methods
  return localStorage.getItem('sfg-admin-access') === 'true' ||
         sessionStorage.getItem('sfg-admin-session') === 'true' ||
         window.location.search.includes('admin=true');
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SecureAdminAccess;
}
