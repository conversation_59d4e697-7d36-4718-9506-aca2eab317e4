<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Skip Links Test - Sisters for Good</title>
  <link rel="stylesheet" href="css/main.css">
  <style>
    /* Test page specific styles */
    .test-content {
      padding: 2rem;
      margin: 2rem 0;
      background: #f9f9f9;
      border-radius: 8px;
    }
    .test-instructions {
      background: #e3f2fd;
      padding: 1rem;
      border-radius: 4px;
      margin-bottom: 2rem;
      border-left: 4px solid #2196f3;
    }
  </style>
</head>
<body>
  <!-- Skip links will be automatically added by accessibility.js -->

  <header id="header" class="bg-gradient-to-r from-pink-600 to-purple-600 text-white p-4">
    <h1>Skip Links Test Page</h1>
    <nav id="navigation" aria-label="Main navigation">
      <ul class="flex space-x-4 mt-2">
        <li><a href="index.html" class="text-white hover:underline">Home</a></li>
        <li><a href="about.html" class="text-white hover:underline">About</a></li>
        <li><a href="programs.html" class="text-white hover:underline">Programs</a></li>
        <li><a href="events.html" class="text-white hover:underline">Events</a></li>
      </ul>
    </nav>
  </header>

  <main id="main-content" role="main" class="container mx-auto px-4 py-8">
    <div class="test-instructions">
      <h2>Testing Skip Links Accessibility</h2>
      <p><strong>Instructions for testing:</strong></p>
      <ol class="list-decimal list-inside space-y-2 mt-2">
        <li><strong>Keyboard Test:</strong> Press the <kbd>Tab</kbd> key immediately after this page loads. You should see skip links appear at the top of the page.</li>
        <li><strong>Visual Test:</strong> The skip links should NOT be visible when using a mouse or when not focused.</li>
        <li><strong>Functionality Test:</strong> When focused, click on each skip link to ensure they navigate to the correct sections.</li>
        <li><strong>Screen Reader Test:</strong> If using a screen reader, the skip links should be announced and accessible.</li>
      </ol>
    </div>

    <div class="test-content">
      <h2>Main Content Section</h2>
      <p>This is the main content area. The "Skip to main content" link should jump directly here.</p>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
      
      <h3>Subsection</h3>
      <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
      
      <h3>Another Subsection</h3>
      <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
    </div>

    <div class="test-content">
      <h2>Testing Results</h2>
      <div class="space-y-4">
        <div class="p-4 bg-green-50 border border-green-200 rounded">
          <h4 class="font-bold text-green-800">✅ Expected Behavior:</h4>
          <ul class="list-disc list-inside text-green-700 mt-2">
            <li>Skip links are invisible by default</li>
            <li>Skip links become visible when focused with Tab key</li>
            <li>Skip links have proper styling (pink background, white text)</li>
            <li>Skip links navigate to correct sections when activated</li>
            <li>Skip links work with screen readers</li>
          </ul>
        </div>
        
        <div class="p-4 bg-red-50 border border-red-200 rounded">
          <h4 class="font-bold text-red-800">❌ Issues to Check:</h4>
          <ul class="list-disc list-inside text-red-700 mt-2">
            <li>Skip links visible without keyboard focus</li>
            <li>Skip links not appearing when tabbing</li>
            <li>Skip links not navigating to correct sections</li>
            <li>Poor contrast or styling issues</li>
            <li>Skip links not accessible to screen readers</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="test-content">
      <h2>Additional Interactive Elements</h2>
      <p>These elements help test the tab order and ensure skip links appear first:</p>
      <div class="space-y-4">
        <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Test Button 1</button>
        <input type="text" placeholder="Test input field" class="border border-gray-300 px-3 py-2 rounded">
        <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">Test Button 2</button>
        <a href="#" class="text-blue-600 hover:underline">Test Link</a>
      </div>
    </div>
  </main>

  <footer id="footer" role="contentinfo" class="bg-gray-800 text-white p-8 mt-8">
    <div class="container mx-auto">
      <h2>Footer Section</h2>
      <p>This is the footer area. The "Skip to footer" link should jump directly here.</p>
      <div class="mt-4">
        <p>&copy; 2024 Sisters for Good. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="js/accessibility.js"></script>
  
  <script>
    // Additional test script to verify skip links functionality
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Skip Links Test Page Loaded');
      
      // Check if skip links were created
      setTimeout(() => {
        const skipLinks = document.querySelector('.skip-links');
        const skipLinkElements = document.querySelectorAll('.skip-link');
        
        console.log('Skip links container:', skipLinks);
        console.log('Number of skip links:', skipLinkElements.length);
        
        if (skipLinkElements.length > 0) {
          console.log('✅ Skip links successfully created');
          skipLinkElements.forEach((link, index) => {
            console.log(`Skip link ${index + 1}:`, link.textContent, '→', link.href);
          });
        } else {
          console.log('❌ Skip links not found - check accessibility.js');
        }
        
        // Test landmark elements
        const mainContent = document.getElementById('main-content');
        const navigation = document.getElementById('navigation');
        const footer = document.getElementById('footer');
        
        console.log('Main content element:', mainContent ? '✅ Found' : '❌ Missing');
        console.log('Navigation element:', navigation ? '✅ Found' : '❌ Missing');
        console.log('Footer element:', footer ? '✅ Found' : '❌ Missing');
      }, 100);
    });
    
    // Test skip link visibility on focus
    document.addEventListener('focusin', function(e) {
      if (e.target.classList.contains('skip-link')) {
        console.log('✅ Skip link focused:', e.target.textContent);
        console.log('Skip link styles:', window.getComputedStyle(e.target));
      }
    });
  </script>
</body>
</html>
