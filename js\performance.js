/**
 * Performance Optimization System
 * Handles image optimization, lazy loading, and performance monitoring
 */

class PerformanceOptimizer {
  constructor() {
    this.imageObserver = null;
    this.performanceMetrics = {};
    this.init();
  }

  init() {
    this.setupLazyLoading();
    this.optimizeImages();
    this.setupCriticalResourcePreloading();
    this.monitorPerformance();
    this.setupServiceWorker();
  }

  setupLazyLoading() {
    // Create intersection observer for lazy loading
    if ('IntersectionObserver' in window) {
      this.imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.loadImage(entry.target);
            this.imageObserver.unobserve(entry.target);
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      // Observe all images with data-src attribute
      this.observeImages();
    } else {
      // Fallback for browsers without IntersectionObserver
      this.loadAllImages();
    }
  }

  observeImages() {
    const lazyImages = document.querySelectorAll('img[data-src], img[loading="lazy"]');
    lazyImages.forEach(img => {
      // Add lazy loading attributes if not present
      if (!img.hasAttribute('data-src') && img.src) {
        img.setAttribute('data-src', img.src);
        img.src = this.generatePlaceholder(img.width || 300, img.height || 200);
      }
      
      // Add loading class for smooth transition
      img.classList.add('lazy-loading');
      
      this.imageObserver.observe(img);
    });
  }

  loadImage(img) {
    const src = img.getAttribute('data-src');
    if (!src) return;

    // Create new image to preload
    const imageLoader = new Image();
    
    imageLoader.onload = () => {
      img.src = src;
      img.classList.remove('lazy-loading');
      img.classList.add('lazy-loaded');
      
      // Remove data-src attribute
      img.removeAttribute('data-src');
      
      // Trigger custom event
      img.dispatchEvent(new CustomEvent('imageLoaded', {
        detail: { src: src }
      }));
    };

    imageLoader.onerror = () => {
      img.classList.add('lazy-error');
      console.warn('Failed to load image:', src);
    };

    imageLoader.src = src;
  }

  loadAllImages() {
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => this.loadImage(img));
  }

  generatePlaceholder(width, height) {
    // Generate SVG placeholder
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#fc5c7d;stop-opacity:0.1" />
            <stop offset="100%" style="stop-color:#6a82fb;stop-opacity:0.1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999" font-family="Arial, sans-serif" font-size="14">Loading...</text>
      </svg>
    `;
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }

  optimizeImages() {
    // Add responsive image attributes
    const images = document.querySelectorAll('img:not([srcset])');
    images.forEach(img => {
      if (img.src && !img.hasAttribute('data-optimized')) {
        this.addResponsiveAttributes(img);
        img.setAttribute('data-optimized', 'true');
      }
    });
  }

  addResponsiveAttributes(img) {
    const src = img.src;
    if (src.includes('unsplash.com')) {
      // Generate responsive srcset for Unsplash images
      const baseUrl = src.split('?')[0];
      const srcset = [
        `${baseUrl}?w=400&q=80 400w`,
        `${baseUrl}?w=800&q=80 800w`,
        `${baseUrl}?w=1200&q=80 1200w`,
        `${baseUrl}?w=1600&q=80 1600w`
      ].join(', ');
      
      img.setAttribute('srcset', srcset);
      img.setAttribute('sizes', '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw');
    }

    // Add loading attribute for modern browsers
    img.setAttribute('loading', 'lazy');
    img.setAttribute('decoding', 'async');
  }

  setupCriticalResourcePreloading() {
    // Preload critical resources
    const criticalResources = [
      { href: 'css/main.css', as: 'style' },
      { href: 'js/main.js', as: 'script' },
      { href: 'https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap', as: 'style' }
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      if (resource.as === 'style') {
        link.onload = () => {
          link.rel = 'stylesheet';
        };
      }
      document.head.appendChild(link);
    });
  }

  monitorPerformance() {
    // Monitor Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.performanceMetrics.lcp = lastEntry.startTime;
        this.reportMetric('LCP', lastEntry.startTime);
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          this.performanceMetrics.fid = entry.processingStart - entry.startTime;
          this.reportMetric('FID', entry.processingStart - entry.startTime);
        });
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.performanceMetrics.cls = clsValue;
        this.reportMetric('CLS', clsValue);
      }).observe({ entryTypes: ['layout-shift'] });
    }

    // Monitor page load time
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        this.performanceMetrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
        this.reportMetric('Load Time', this.performanceMetrics.loadTime);
      }, 0);
    });
  }

  reportMetric(name, value) {
    // Report to analytics (placeholder for actual analytics integration)
    console.log(`Performance Metric - ${name}:`, value);
    
    // Store in localStorage for debugging
    const metrics = JSON.parse(localStorage.getItem('performance-metrics') || '{}');
    metrics[name] = value;
    metrics.timestamp = Date.now();
    localStorage.setItem('performance-metrics', JSON.stringify(metrics));
  }

  setupServiceWorker() {
    // Register service worker for caching
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('SW registered: ', registration);
          })
          .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  }

  // Public methods for manual optimization
  optimizeAllImages() {
    this.observeImages();
  }

  getPerformanceMetrics() {
    return this.performanceMetrics;
  }

  // Utility method to compress images client-side
  compressImage(file, quality = 0.8, maxWidth = 1200) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        canvas.toBlob(resolve, 'image/jpeg', quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }
}

// Initialize performance optimizer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.performanceOptimizer = new PerformanceOptimizer();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PerformanceOptimizer;
}
