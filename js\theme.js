/**
 * Sisters for Good - Dark Theme Toggle System
 * Handles theme switching and persistence across all pages
 */

class ThemeManager {
  constructor() {
    this.currentTheme = 'light';
    this.init();
  }

  init() {
    // Load saved theme or default to light
    this.loadTheme();
    
    // Apply theme to document
    this.applyTheme();
    
    // Initialize theme toggle button
    this.initToggleButton();
    
    // Listen for system theme changes
    this.listenForSystemThemeChanges();
  }

  loadTheme() {
    // Check localStorage first
    const savedTheme = localStorage.getItem('sisters-for-good-theme');
    
    if (savedTheme) {
      this.currentTheme = savedTheme;
    } else {
      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.currentTheme = prefersDark ? 'dark' : 'light';
    }
  }

  applyTheme() {
    document.documentElement.setAttribute('data-theme', this.currentTheme);
    
    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor();
    
    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { theme: this.currentTheme }
    }));
  }

  updateMetaThemeColor() {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }
    
    // Set theme color based on current theme
    const themeColor = this.currentTheme === 'dark' ? '#0f172a' : '#ffffff';
    metaThemeColor.content = themeColor;
  }

  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.saveTheme();
    this.applyTheme();
    this.updateToggleButton();
  }

  saveTheme() {
    localStorage.setItem('sisters-for-good-theme', this.currentTheme);
  }

  initToggleButton() {
    // Create toggle button if it doesn't exist
    let toggleButton = document.getElementById('theme-toggle');
    
    if (!toggleButton) {
      this.createToggleButton();
      toggleButton = document.getElementById('theme-toggle');
    }
    
    // Add click event listener
    toggleButton.addEventListener('click', () => {
      this.toggleTheme();
    });
    
    // Update button state
    this.updateToggleButton();
  }

  createToggleButton() {
    const toggleButton = document.createElement('button');
    toggleButton.id = 'theme-toggle';
    toggleButton.className = 'theme-toggle';
    toggleButton.setAttribute('aria-label', 'Toggle dark mode');
    toggleButton.setAttribute('title', 'Toggle dark/light theme');
    
    toggleButton.innerHTML = `
      <svg class="sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
      </svg>
      <svg class="moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
      </svg>
    `;
    
    // Find the header navigation and add the button
    const headerNav = document.querySelector('header .flex.justify-between.items-center');
    if (headerNav) {
      // Create a container for the theme toggle
      const themeContainer = document.createElement('div');
      themeContainer.className = 'flex items-center ml-4';
      themeContainer.appendChild(toggleButton);
      
      // Insert before the mobile menu button
      const mobileMenuBtn = document.getElementById('mobileMenuBtn');
      if (mobileMenuBtn) {
        headerNav.insertBefore(themeContainer, mobileMenuBtn);
      } else {
        headerNav.appendChild(themeContainer);
      }
    }
  }

  updateToggleButton() {
    const toggleButton = document.getElementById('theme-toggle');
    if (toggleButton) {
      const title = this.currentTheme === 'light' ? 'Switch to dark mode' : 'Switch to light mode';
      toggleButton.setAttribute('title', title);
      toggleButton.setAttribute('aria-label', title);
    }
  }

  listenForSystemThemeChanges() {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    mediaQuery.addEventListener('change', (e) => {
      // Only auto-switch if user hasn't manually set a preference
      if (!localStorage.getItem('sisters-for-good-theme')) {
        this.currentTheme = e.matches ? 'dark' : 'light';
        this.applyTheme();
        this.updateToggleButton();
      }
    });
  }

  // Public method to get current theme
  getCurrentTheme() {
    return this.currentTheme;
  }

  // Public method to set theme programmatically
  setTheme(theme) {
    if (theme === 'light' || theme === 'dark') {
      this.currentTheme = theme;
      this.saveTheme();
      this.applyTheme();
      this.updateToggleButton();
    }
  }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.themeManager = new ThemeManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeManager;
}
