<!-- Breadcrumb Navigation Component -->
<nav aria-label="Breadcrumb" class="bg-gray-50 dark:bg-slate-700 border-b border-gray-200 dark:border-gray-600 py-3 transition-colors duration-300">
  <div class="container mx-auto px-4">
    <ol id="breadcrumb-list" class="flex items-center space-x-2 text-sm">
      <!-- Breadcrumb items will be dynamically generated here -->
    </ol>
  </div>
</nav>

<script>
/**
 * Breadcrumb Navigation System
 * Automatically generates breadcrumb navigation based on current page
 */
class BreadcrumbNavigation {
  constructor() {
    this.breadcrumbData = {
      'index.html': {
        title: 'Home',
        path: []
      },
      'about.html': {
        title: 'About Us',
        path: [
          { title: 'Home', url: 'index.html' }
        ]
      },
      'programs.html': {
        title: 'Programs',
        path: [
          { title: 'Home', url: 'index.html' }
        ]
      },
      'events.html': {
        title: 'Events',
        path: [
          { title: 'Home', url: 'index.html' }
        ]
      },
      'health.html': {
        title: 'Health Resources',
        path: [
          { title: 'Home', url: 'index.html' }
        ]
      },
      'contact.html': {
        title: 'Contact',
        path: [
          { title: 'Home', url: 'index.html' }
        ]
      },
      'donate.html': {
        title: 'Donate',
        path: [
          { title: 'Home', url: 'index.html' }
        ]
      }
    };
    
    this.init();
  }

  init() {
    this.generateBreadcrumb();
  }

  getCurrentPage() {
    const path = window.location.pathname;
    const filename = path.split('/').pop() || 'index.html';
    return filename === '' ? 'index.html' : filename;
  }

  generateBreadcrumb() {
    const breadcrumbList = document.getElementById('breadcrumb-list');
    if (!breadcrumbList) return;

    const currentPage = this.getCurrentPage();
    const pageData = this.breadcrumbData[currentPage];
    
    if (!pageData) {
      // Hide breadcrumb for unknown pages
      const breadcrumbNav = breadcrumbList.closest('nav');
      if (breadcrumbNav) {
        breadcrumbNav.style.display = 'none';
      }
      return;
    }

    // Clear existing breadcrumbs
    breadcrumbList.innerHTML = '';

    // Add path items
    pageData.path.forEach((item, index) => {
      this.addBreadcrumbItem(breadcrumbList, item.title, item.url, false);
      this.addSeparator(breadcrumbList);
    });

    // Add current page (not clickable)
    this.addBreadcrumbItem(breadcrumbList, pageData.title, null, true);

    // Hide breadcrumb on home page
    if (currentPage === 'index.html') {
      const breadcrumbNav = breadcrumbList.closest('nav');
      if (breadcrumbNav) {
        breadcrumbNav.style.display = 'none';
      }
    }
  }

  addBreadcrumbItem(container, title, url, isCurrent) {
    const li = document.createElement('li');
    li.className = 'flex items-center';

    if (isCurrent) {
      li.innerHTML = `
        <span class="text-gray-600 dark:text-gray-300 font-medium" aria-current="page">
          ${title}
        </span>
      `;
    } else {
      li.innerHTML = `
        <a href="${url}" class="text-pink-600 dark:text-pink-400 hover:text-pink-800 dark:hover:text-pink-300 transition-colors font-medium">
          ${title}
        </a>
      `;
    }

    container.appendChild(li);
  }

  addSeparator(container) {
    const li = document.createElement('li');
    li.className = 'flex items-center';
    li.innerHTML = `
      <svg class="h-4 w-4 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    `;
    container.appendChild(li);
  }
}

// Initialize breadcrumb navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new BreadcrumbNavigation();
});
</script>

<style>
/* Breadcrumb specific styles */
#breadcrumb-list a:hover {
  text-decoration: underline;
}

#breadcrumb-list svg {
  flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  #breadcrumb-list {
    font-size: 0.875rem;
  }
  
  #breadcrumb-list svg {
    height: 0.875rem;
    width: 0.875rem;
    margin: 0 0.375rem;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  nav[aria-label="Breadcrumb"] {
    background-color: rgb(51 65 85);
    border-color: rgb(75 85 99);
  }
}

/* Accessibility improvements */
#breadcrumb-list a:focus {
  outline: 2px solid #fc5c7d;
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* Animation for breadcrumb appearance */
nav[aria-label="Breadcrumb"] {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
