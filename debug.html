<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Debug Components</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Debug Components</h1>
  
  <h2>Header Content:</h2>
  <pre id="header-content">Loading...</pre>
  
  <h2>Footer Content:</h2>
  <pre id="footer-content">Loading...</pre>
  
  <script>
    // Load header
    fetch('components/header.html')
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
      })
      .then(data => {
        document.getElementById('header-content').textContent = data;
        console.log('Header loaded successfully');
      })
      .catch(error => {
        document.getElementById('header-content').textContent = `Error loading header: ${error.message}`;
        console.error('Error loading header:', error);
      });
    
    // Load footer
    fetch('components/footer.html')
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
      })
      .then(data => {
        document.getElementById('footer-content').textContent = data;
        console.log('Footer loaded successfully');
      })
      .catch(error => {
        document.getElementById('footer-content').textContent = `Error loading footer: ${error.message}`;
        console.error('Error loading footer:', error);
      });
  </script>
</body>
</html>
