/**
 * Accessibility Enhancement System
 * Improves website accessibility with skip links, ARIA labels, and keyboard navigation
 */

class AccessibilityEnhancer {
  constructor() {
    this.init();
  }

  init() {
    this.addSkipLinks();
    this.enhanceARIALabels();
    this.improveKeyboardNavigation();
    this.addFocusManagement();
    this.enhanceHeadingHierarchy();
    this.addLiveRegions();
  }

  addSkipLinks() {
    // Create skip links container
    const skipLinks = document.createElement('div');
    skipLinks.className = 'skip-links';
    skipLinks.innerHTML = `
      <a href="#main-content" class="skip-link">Skip to main content</a>
      <a href="#navigation" class="skip-link">Skip to navigation</a>
      <a href="#footer" class="skip-link">Skip to footer</a>
    `;

    // Insert at the beginning of body
    document.body.insertBefore(skipLinks, document.body.firstChild);

    // Add main content landmark if it doesn't exist
    this.addMainContentLandmark();
  }

  addMainContentLandmark() {
    let mainContent = document.getElementById('main-content');
    
    if (!mainContent) {
      // Find the first section after header or create main wrapper
      const header = document.querySelector('header');
      const firstSection = header ? header.nextElementSibling : document.querySelector('section');
      
      if (firstSection) {
        // Wrap content in main element
        mainContent = document.createElement('main');
        mainContent.id = 'main-content';
        mainContent.setAttribute('role', 'main');
        
        // Move all content after header into main
        const elementsToMove = [];
        let currentElement = firstSection;
        
        while (currentElement && currentElement.tagName !== 'FOOTER') {
          elementsToMove.push(currentElement);
          currentElement = currentElement.nextElementSibling;
        }
        
        elementsToMove.forEach(element => {
          mainContent.appendChild(element);
        });
        
        // Insert main content after header
        if (header) {
          header.parentNode.insertBefore(mainContent, header.nextSibling);
        } else {
          document.body.insertBefore(mainContent, document.body.firstChild);
        }
      }
    }
  }

  enhanceARIALabels() {
    // Enhance navigation
    const nav = document.querySelector('nav');
    if (nav && !nav.getAttribute('aria-label')) {
      nav.setAttribute('aria-label', 'Main navigation');
      nav.setAttribute('role', 'navigation');
    }

    // Add navigation ID for skip links
    if (nav && !nav.id) {
      nav.id = 'navigation';
    }

    // Enhance footer
    const footer = document.querySelector('footer');
    if (footer) {
      footer.setAttribute('role', 'contentinfo');
      if (!footer.id) {
        footer.id = 'footer';
      }
    }

    // Enhance form labels
    this.enhanceFormLabels();

    // Enhance buttons
    this.enhanceButtons();

    // Enhance images
    this.enhanceImages();
  }

  enhanceFormLabels() {
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
      if (!input.getAttribute('aria-label') && !input.getAttribute('aria-labelledby')) {
        const label = document.querySelector(`label[for="${input.id}"]`);
        if (!label && input.placeholder) {
          input.setAttribute('aria-label', input.placeholder);
        }
      }

      // Add required indicator
      if (input.required && !input.getAttribute('aria-required')) {
        input.setAttribute('aria-required', 'true');
      }
    });
  }

  enhanceButtons() {
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
      // Ensure buttons have accessible names
      if (!button.textContent.trim() && !button.getAttribute('aria-label') && !button.getAttribute('aria-labelledby')) {
        const icon = button.querySelector('svg');
        if (icon) {
          button.setAttribute('aria-label', 'Button');
        }
      }

      // Add role if needed
      if (!button.getAttribute('role') && button.tagName !== 'BUTTON') {
        button.setAttribute('role', 'button');
      }
    });
  }

  enhanceImages() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      // Ensure all images have alt text
      if (!img.getAttribute('alt')) {
        // If decorative, add empty alt
        if (img.closest('.decoration') || img.classList.contains('decoration')) {
          img.setAttribute('alt', '');
        } else {
          // Try to derive alt text from context
          const figcaption = img.closest('figure')?.querySelector('figcaption');
          if (figcaption) {
            img.setAttribute('alt', figcaption.textContent.trim());
          } else {
            img.setAttribute('alt', 'Image');
          }
        }
      }
    });
  }

  improveKeyboardNavigation() {
    // Ensure all interactive elements are keyboard accessible
    const interactiveElements = document.querySelectorAll('a, button, input, textarea, select, [tabindex]');
    
    interactiveElements.forEach(element => {
      // Add focus styles if missing
      if (!element.style.outline && !element.classList.contains('focus-visible')) {
        element.addEventListener('focus', this.addFocusStyle);
        element.addEventListener('blur', this.removeFocusStyle);
      }

      // Handle Enter key for buttons that aren't real buttons
      if (element.getAttribute('role') === 'button' && element.tagName !== 'BUTTON') {
        element.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            element.click();
          }
        });
      }
    });

    // Add keyboard navigation for dropdowns and modals
    this.addModalKeyboardSupport();
  }

  addFocusStyle(event) {
    event.target.style.outline = '2px solid #fc5c7d';
    event.target.style.outlineOffset = '2px';
  }

  removeFocusStyle(event) {
    event.target.style.outline = '';
    event.target.style.outlineOffset = '';
  }

  addModalKeyboardSupport() {
    // Handle search modal keyboard navigation
    document.addEventListener('keydown', (e) => {
      const searchModal = document.getElementById('search-modal');
      if (searchModal && !searchModal.classList.contains('hidden')) {
        if (e.key === 'Tab') {
          this.trapFocus(e, searchModal);
        }
      }
    });
  }

  trapFocus(event, container) {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  addFocusManagement() {
    // Manage focus for dynamic content
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // If a modal or dialog is added, focus the first focusable element
              if (node.matches('[role="dialog"], .modal, #search-modal')) {
                setTimeout(() => {
                  const firstFocusable = node.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                  if (firstFocusable) {
                    firstFocusable.focus();
                  }
                }, 100);
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  enhanceHeadingHierarchy() {
    // Check and fix heading hierarchy
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let currentLevel = 0;

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      
      // Add IDs for anchor links if missing
      if (!heading.id && heading.textContent.trim()) {
        heading.id = this.generateHeadingId(heading.textContent);
      }

      // Check for proper hierarchy
      if (level > currentLevel + 1 && index > 0) {
        console.warn(`Heading hierarchy issue: ${heading.tagName} follows h${currentLevel}`, heading);
      }
      
      currentLevel = level;
    });
  }

  generateHeadingId(text) {
    return text.toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  addLiveRegions() {
    // Add live region for dynamic content announcements
    const liveRegion = document.createElement('div');
    liveRegion.id = 'live-region';
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    document.body.appendChild(liveRegion);

    // Add status region for form feedback
    const statusRegion = document.createElement('div');
    statusRegion.id = 'status-region';
    statusRegion.setAttribute('aria-live', 'assertive');
    statusRegion.setAttribute('aria-atomic', 'true');
    statusRegion.className = 'sr-only';
    document.body.appendChild(statusRegion);

    // Make live region globally accessible
    window.announceToScreenReader = (message, isUrgent = false) => {
      const region = isUrgent ? statusRegion : liveRegion;
      region.textContent = message;
      setTimeout(() => {
        region.textContent = '';
      }, 1000);
    };
  }
}

// Initialize accessibility enhancements when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.accessibilityEnhancer = new AccessibilityEnhancer();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AccessibilityEnhancer;
}
