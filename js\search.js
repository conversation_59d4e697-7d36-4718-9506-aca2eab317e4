/**
 * Sisters for Good - Search Functionality
 * Provides comprehensive search across all website content
 */

class SiteSearch {
  constructor() {
    this.searchData = [];
    this.searchIndex = new Map();
    this.isInitialized = false;
    this.init();
  }

  async init() {
    try {
      await this.buildSearchIndex();
      this.setupSearchUI();
      this.isInitialized = true;
    } catch (error) {
      console.error('Search initialization failed:', error);
    }
  }

  async buildSearchIndex() {
    // Define searchable content for each page
    this.searchData = [
      {
        title: "Home - Sisters for Good",
        url: "index.html",
        content: "Sisters for Good empowers women strengthens communities cosmetology education mentorship programs Pottstown PA women empowerment community support",
        category: "main",
        description: "Empowering women and strengthening communities through collaborative initiatives"
      },
      {
        title: "About Us - Our Mission & Team",
        url: "about.html", 
        content: "about mission vision team She<PERSON> founder 10 years empowering women cosmetology education community development nonprofit organization",
        category: "about",
        description: "Learn about our 10+ year mission empowering women through education and community support"
      },
      {
        title: "Programs - Cosmetology Education & Mentorship",
        url: "programs.html",
        content: "programs cosmetology education school partnerships mentorship community engagement initiatives training workshops career development",
        category: "programs", 
        description: "Discover our empowering programs: cosmetology education, mentorship, and community engagement"
      },
      {
        title: "Events & Calendar - Father's Day Brunch & More",
        url: "events.html",
        content: "events calendar Father's Day Brunch workshops seminars community gatherings RSVP fundraisers third Sunday June 247E High Street Pottstown",
        category: "events",
        description: "Join our events: Father's Day Brunch, workshops, and community gatherings"
      },
      {
        title: "Health Resources - Tobacco-Free Living",
        url: "health.html",
        content: "health resources tobacco-free living awareness education wellness community health information prevention smoking cessation",
        category: "health",
        description: "Access health resources and tobacco-free living information"
      },
      {
        title: "Contact Us - Get in Touch",
        url: "contact.html",
        content: "contact us get in touch 247 East High Street Pottstown PA 19464 phone ************ email volunteer partnership opportunities",
        category: "contact",
        description: "Contact Sisters for Good for inquiries, volunteer opportunities, and partnerships"
      },
      {
        title: "Donate - Support Our Mission",
        url: "donate.html",
        content: "donate support mission financial contributions volunteer time community impact women empowerment funding programs",
        category: "donate",
        description: "Support Sisters for Good through donations and volunteer opportunities"
      }
    ];

    // Build search index for faster searching
    this.searchData.forEach((item, index) => {
      const words = (item.title + ' ' + item.content + ' ' + item.description).toLowerCase().split(/\s+/);
      words.forEach(word => {
        word = word.replace(/[^\w]/g, ''); // Remove punctuation
        if (word.length > 2) { // Only index words longer than 2 characters
          if (!this.searchIndex.has(word)) {
            this.searchIndex.set(word, []);
          }
          this.searchIndex.get(word).push(index);
        }
      });
    });
  }

  setupSearchUI() {
    // Create search button for header
    this.createSearchButton();
    
    // Create search modal
    this.createSearchModal();
    
    // Setup event listeners
    this.setupEventListeners();
  }

  createSearchButton() {
    const headers = document.querySelectorAll('header nav');
    headers.forEach(header => {
      if (!header.querySelector('.search-button')) {
        const searchButton = document.createElement('button');
        searchButton.className = 'search-button hidden md:flex items-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors';
        searchButton.innerHTML = `
          <svg class="h-5 w-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">Search</span>
        `;
        searchButton.setAttribute('aria-label', 'Search website');
        searchButton.setAttribute('title', 'Search Sisters for Good website');
        
        // Insert before theme toggle button
        const themeToggle = header.parentElement.querySelector('#theme-toggle');
        if (themeToggle) {
          themeToggle.parentElement.insertBefore(searchButton, themeToggle);
        } else {
          header.parentElement.appendChild(searchButton);
        }
      }
    });
  }

  createSearchModal() {
    if (document.getElementById('search-modal')) return;

    const modal = document.createElement('div');
    modal.id = 'search-modal';
    modal.className = 'fixed inset-0 z-50 hidden bg-black bg-opacity-50 flex items-start justify-center pt-16';
    modal.innerHTML = `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-96 overflow-hidden transition-colors duration-300">
        <div class="p-4 border-b border-gray-200 dark:border-gray-600">
          <div class="flex items-center">
            <svg class="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input type="text" id="search-input" placeholder="Search Sisters for Good..." 
                   class="flex-1 border-none outline-none text-lg bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400">
            <button id="search-close" class="ml-3 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
              <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        <div id="search-results" class="max-h-80 overflow-y-auto">
          <div class="p-4 text-center text-gray-500 dark:text-gray-400">
            <svg class="h-12 w-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <p>Start typing to search...</p>
          </div>
        </div>
      </div>
    `;
    document.body.appendChild(modal);
  }

  setupEventListeners() {
    // Search button click
    document.addEventListener('click', (e) => {
      if (e.target.closest('.search-button')) {
        this.openSearch();
      }
      if (e.target.closest('#search-close')) {
        this.closeSearch();
      }
      if (e.target.id === 'search-modal') {
        this.closeSearch();
      }
    });

    // Search input
    document.addEventListener('input', (e) => {
      if (e.target.id === 'search-input') {
        this.performSearch(e.target.value);
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + K to open search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        this.openSearch();
      }
      // Escape to close search
      if (e.key === 'Escape') {
        this.closeSearch();
      }
    });
  }

  openSearch() {
    const modal = document.getElementById('search-modal');
    const input = document.getElementById('search-input');
    if (modal && input) {
      modal.classList.remove('hidden');
      input.focus();
      document.body.style.overflow = 'hidden';
    }
  }

  closeSearch() {
    const modal = document.getElementById('search-modal');
    const input = document.getElementById('search-input');
    if (modal) {
      modal.classList.add('hidden');
      document.body.style.overflow = '';
      if (input) input.value = '';
      this.clearResults();
    }
  }

  performSearch(query) {
    const resultsContainer = document.getElementById('search-results');
    if (!resultsContainer || !query.trim()) {
      this.clearResults();
      return;
    }

    const results = this.search(query.trim());
    this.displayResults(results, query);
  }

  search(query) {
    const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 1);
    const resultScores = new Map();

    queryWords.forEach(word => {
      // Exact matches
      if (this.searchIndex.has(word)) {
        this.searchIndex.get(word).forEach(index => {
          resultScores.set(index, (resultScores.get(index) || 0) + 10);
        });
      }

      // Partial matches
      this.searchIndex.forEach((indices, indexedWord) => {
        if (indexedWord.includes(word) && indexedWord !== word) {
          indices.forEach(index => {
            resultScores.set(index, (resultScores.get(index) || 0) + 5);
          });
        }
      });
    });

    // Convert to array and sort by score
    return Array.from(resultScores.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([index]) => this.searchData[index])
      .slice(0, 6); // Limit to top 6 results
  }

  displayResults(results, query) {
    const resultsContainer = document.getElementById('search-results');
    
    if (results.length === 0) {
      resultsContainer.innerHTML = `
        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
          <svg class="h-12 w-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p>No results found for "${query}"</p>
          <p class="text-sm mt-1">Try different keywords or check spelling</p>
        </div>
      `;
      return;
    }

    const resultsHTML = results.map(result => `
      <a href="${result.url}" class="block p-4 hover:bg-gray-50 dark:hover:bg-slate-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0 transition-colors">
        <div class="flex items-start">
          <div class="flex-1">
            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">${this.highlightQuery(result.title, query)}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">${this.highlightQuery(result.description, query)}</p>
            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <span class="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-2 py-1 rounded-full mr-2">${result.category}</span>
              <span>${result.url}</span>
            </div>
          </div>
          <svg class="h-4 w-4 text-gray-400 ml-2 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </div>
      </a>
    `).join('');

    resultsContainer.innerHTML = resultsHTML;
  }

  highlightQuery(text, query) {
    if (!query) return text;
    const regex = new RegExp(`(${query.split(/\s+/).join('|')})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>');
  }

  clearResults() {
    const resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
      resultsContainer.innerHTML = `
        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
          <svg class="h-12 w-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <p>Start typing to search...</p>
        </div>
      `;
    }
  }
}

// Initialize search when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.siteSearch = new SiteSearch();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SiteSearch;
}
