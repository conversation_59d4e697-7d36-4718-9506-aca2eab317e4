import requests
from pprint import pprint
from datetime import datetime, timedelta

def get_historical_whois(domain, api_key, years=7):
    """
    Fetches the historical WHOIS data for a given domain.

    Args:
        domain (str): The domain name to query.
        api_key (str): Your API key for the WHOIS service.
        years (int): Number of years of history to retrieve.

    Returns:
        list: A list of WHOIS records within the specified timeframe.
    """
    url = "https://www.whoisxmlapi.com/whoisserver/WhoisService"

    # Calculate the date range
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=years*365)

    params = {
        'apiKey': api_key,
        'domainName': domain,
        'history': 'true',
        'outputFormat': 'JSON'
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()

        if 'WhoisRecord' not in data:
            print("No WHOIS records found.")
            return []

        whois_records = data['WhoisRecord'].get('historicalWhoisRecords', {}).get('records', [])

        # Filter records within the past 'years' years
        filtered_records = []
        for record in whois_records:
            try:
                record_date = datetime.strptime(record['updatedDate'], "%Y-%m-%dT%H:%M:%SZ")
                if start_date <= record_date <= end_date:
                    filtered_records.append(record)
            except (KeyError, ValueError):
                continue

        return filtered_records

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return []

def main():
    domain = input("Enter the domain name (e.g., example.com): ").strip()
    api_key = "YOUR_API_KEY"  # Replace with your actual API key

    print(f"Fetching ownership history for {domain} over the past 7 years...\n")
    history = get_historical_whois(domain, api_key, years=7)

    if not history:
        print("No historical WHOIS data available for this domain.")
        return

    # Pretty print the ownership history
    ownership_history = []
    for record in history:
        ownership = {
            'Registrar': record.get('registrarName', 'N/A'),
            'Registered On': record.get('createdDate', 'N/A'),
            'Updated On': record.get('updatedDate', 'N/A'),
            'Expires On': record.get('expiresDate', 'N/A'),
            'Registrant Name': record.get('registrant', {}).get('name', 'N/A'),
            'Registrant Organization': record.get('registrant', {}).get('organization', 'N/A'),
            'Registrant Email': record.get('registrant', {}).get('email', 'N/A'),
            'Status': record.get('status', 'N/A')
        }
        ownership_history.append(ownership)

    pprint(ownership_history)

if __name__ == "__main__":
    main()
