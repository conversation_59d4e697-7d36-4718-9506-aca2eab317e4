/**
 * Sisters for Good - Contact Form Handler
 * Enhanced form handling with validation and feedback
 */

document.addEventListener('DOMContentLoaded', function() {
  const contactForm = document.getElementById('contactForm');
  const formStatus = document.createElement('div');
  formStatus.className = 'mt-4 p-3 rounded hidden';
  
  // Add the status element after the form
  if (contactForm) {
    contactForm.after(formStatus);
    
    contactForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      // Get form data
      const formData = new FormData(contactForm);
      const formDataObj = {};
      formData.forEach((value, key) => {
        formDataObj[key] = value;
      });
      
      // Validate form data
      if (!validateForm(formDataObj)) {
        return;
      }
      
      // Show loading state
      showStatus('Sending your message...', 'bg-blue-100 text-blue-700');
      
      // Simulate API call (in a real implementation, this would be an actual API call)
      try {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Simulate successful submission
        showStatus('Thank you for your message! We will get back to you soon.', 'bg-green-100 text-green-700');
        contactForm.reset();
        
        // In a real implementation, you would send the data to a server:
        /*
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formDataObj),
        });
        
        if (response.ok) {
          showStatus('Thank you for your message! We will get back to you soon.', 'bg-green-100 text-green-700');
          contactForm.reset();
        } else {
          const errorData = await response.json();
          showStatus(`Error: ${errorData.message || 'Something went wrong'}`, 'bg-red-100 text-red-700');
        }
        */
      } catch (error) {
        console.error('Error submitting form:', error);
        showStatus('An error occurred while sending your message. Please try again later.', 'bg-red-100 text-red-700');
      }
    });
  }
  
  // Validate form data
  function validateForm(data) {
    // Reset previous error messages
    document.querySelectorAll('.error-message').forEach(el => el.remove());
    
    let isValid = true;
    
    // Validate name (required, at least 2 characters)
    if (!data.name || data.name.length < 2) {
      showFieldError('name', 'Please enter your name (at least 2 characters)');
      isValid = false;
    }
    
    // Validate email (required, valid format)
    if (!data.email || !isValidEmail(data.email)) {
      showFieldError('email', 'Please enter a valid email address');
      isValid = false;
    }
    
    // Validate subject (required)
    if (!data.subject) {
      showFieldError('subject', 'Please select a subject');
      isValid = false;
    }
    
    // Validate message (required, at least 10 characters)
    if (!data.message || data.message.length < 10) {
      showFieldError('message', 'Please enter your message (at least 10 characters)');
      isValid = false;
    }
    
    return isValid;
  }
  
  // Show field error
  function showFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message text-red-600 text-sm mt-1';
    errorElement.textContent = message;
    
    field.classList.add('border-red-500');
    field.parentNode.appendChild(errorElement);
    
    // Remove error styling when field is changed
    field.addEventListener('input', function() {
      field.classList.remove('border-red-500');
      const errorMsg = field.parentNode.querySelector('.error-message');
      if (errorMsg) {
        errorMsg.remove();
      }
    });
  }
  
  // Show form status message
  function showStatus(message, className) {
    formStatus.textContent = message;
    formStatus.className = `mt-4 p-3 rounded ${className}`;
    formStatus.classList.remove('hidden');
    
    // Scroll to status message
    formStatus.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
  
  // Validate email format
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
});
