
  /* Custom CSS for a Web3.0 feel */
  body {
    background: linear-gradient(135deg, #6a82fb, #fc5c7d); /* Soft purple to vibrant pink gradient */
    font-family: 'Arial', sans-serif;
    padding-top: 60px; /* Added padding to offset the fixed navbar */
  }
.hero {
  
  background-image: linear-gradient(135deg, #ff007f, #9c4fe0); /* Neon Pink to Purple gradient */
  /* background-image: url('../assets/sfg_2025_4.jpg'); Corrected path */
  background-size: cover;
  background-position: center;
  height: 50vh; /* Default height for larger screens */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 0 10px; /* Added padding for smaller screens */
  text-align: center; /* Ensure text is centered */
}
  
  .hero .circle-overlay {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(252, 92, 125, 0.2); /* Transparent vibrant pink */
    width: 300px;
    height: 300px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  .hero h1 {
    font-size: 5rem;
    color: white;
    font-weight: bold;
  }
  
  .hero p {
    font-size: 1.25rem;
    color: white;
    margin-bottom: 2rem;
  }
  
  .hero .cta-button {
    background-color: #ffee02;
    padding: 15px 25px;
    font-size: 20px;
    border-radius: 5px;
    text-transform: uppercase;
    transition: 0.3s;
  }
  
  .hero .cta-button:hover {
    background-color: #fc5c7d; /* Vibrant Pink */
  }
  
  /* Adjust hero section for smaller screens */
@media (max-width: 768px) {
  .hero {
    height: 60vh; /* Increase height for better visibility on small screens */
    padding: 10px 0; /* Adjust padding for mobile screens */
  }

  .hero h1 {
    font-size: 3rem; /* Smaller font size for heading */
  }

  .hero p {
    font-size: 1.2rem; /* Smaller font size for description */
  }

  .hero .cta-button {
    padding: 12px 20px; /* Adjust button size for mobile */
    font-size: 18px; /* Smaller button text */
  }
}
  .animated-background {
    background: url('../assets/sfg_2025_6.jpg') repeat center;
    background-size: cover;
    height: 300px;
  }
  .circle-overlay {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(252, 92, 125, 0.2); /* Transparent vibrant pink */
    width: 300px;
    height: 300px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  /* Menu bar styles */
  .navbar {
    display: flex;
    justify-content: center;
    background-color: #fc5c7d; /* Dark gray */
    padding: 10px 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
  }
  .navbar a {
    color: white;
    font-size: 24px; /* Larger size for better visibility */
    padding: 10px 15px; /* Adequate spacing for touch devices */
    transition: background-color 0.3s, color 0.3s;
  }
  .navbar a:hover, .navbar a:focus {
    color: #fc5c7d; /* Vibrant pink */
    background-color: rgba(255, 255, 255, 0.2); /* Subtle hover effect for better UX */
  }
  /* Footer styles */
  .footer {
    background-color: #2c3e50; /* Navy blue */
    color: white;
    padding: 40px 20px;
    text-align: center;
  }
  .footer a {
    color: #fc5c7d; /* Vibrant pink */
    text-decoration: none;
    margin: 0 10px;
    font-size: 1.2rem;
  }
  .footer a:hover {
    color: #ffffff; /* White for hover */
  }
  /* Testimonial Mods */
  .testimonial-container {
      display: flex;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      -webkit-overflow-scrolling: touch;
      gap: 20px;
      padding: 20px;
  }

  .testimonial {
      flex: 0 0 auto;
      width: 80%; /* Adjust width as necessary */
      margin: 0 auto;
      background: white;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      padding: 20px;
      border-radius: 10px;
      scroll-snap-align: start;
  }

  /* Scrollbar styling */
  .testimonial-container::-webkit-scrollbar {
      height: 8px;
  }

  .testimonial-container::-webkit-scrollbar-thumb {
      background-color: darkgrey;
      border-radius: 10px;
  }

  /* Hide scrollbar for IE, Edge, and Firefox */
  .testimonial-container {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
  }

  .testimonial-container::-webkit-scrollbar {
      display: none;
  }

  .donate-section {
  text-align: center;
  margin: 20px;
  padding: 20px;
}

.qr-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.qr-item {
    width: 200px; /* Fixed width for each QR item */
    padding: 10px;
    text-align: center;
}

.qr-item img {
    width: 100%; /* Ensure the image fills the container width */
    height: auto; /* Maintain aspect ratio */
}

.qr-item h3 {
    margin-bottom: 10px; /* Spacing between title and QR code */
}

.qr-item p {
    font-size: 0.8rem; /* Smaller text size for the addresses */
    word-wrap: break-word; /* Ensures long addresses wrap and don't overflow */
}

.hidden {
    display: none;
}
.qr-code {
    text-align: center;
    margin-top: 10px;
}
