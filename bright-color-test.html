<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bright Color Scheme Test - Sisters for Good</title>
  
  <!-- Font Loading Optimizer -->
  <script src="js/font-loading-optimizer.js"></script>
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Custom CSS with Bright Colors -->
  <link href="css/main.css" rel="stylesheet">
  
  <style>
    .color-demo {
      padding: 2rem;
      margin: 1rem 0;
      border-radius: 1rem;
      text-align: center;
      font-weight: 600;
      transition: all 0.3s ease;
    }
    
    .color-demo:hover {
      transform: translateY(-4px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .comparison-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin: 2rem 0;
    }
    
    .before-after {
      padding: 1rem;
      border-radius: 0.5rem;
      text-align: center;
    }
    
    .before {
      background: #f8fafc;
      color: #475569;
      border: 1px solid #e2e8f0;
    }
    
    .after {
      background: var(--bg-secondary);
      color: var(--text-secondary);
      border: 2px solid var(--border-color);
    }
    
    .feature-showcase {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin: 3rem 0;
    }
    
    .theme-toggle-demo {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }
  </style>
</head>
<body>
  <!-- Theme Toggle Demo -->
  <div class="theme-toggle-demo">
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
      <svg class="sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
      </svg>
      <svg class="moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
      </svg>
    </button>
  </div>

  <!-- Hero Section with Bright Colors -->
  <section class="hero">
    <div class="hero-content">
      <h1 class="text-5xl font-bold mb-6">🌈 Bright Color Scheme</h1>
      <p class="text-xl mb-8">Experience the vibrant new look of Sisters for Good with enhanced colors and improved visual appeal!</p>
      <div class="space-x-4">
        <button class="btn btn-primary">Primary Button</button>
        <button class="btn btn-secondary">Secondary Button</button>
        <button class="btn btn-accent">Accent Button</button>
      </div>
    </div>
  </section>

  <div class="container mx-auto px-4 py-12">
    <!-- Color Palette Showcase -->
    <section class="mb-16">
      <h2 class="text-4xl font-bold text-center mb-8 text-gray-900">New Bright Color Palette</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="color-demo section-bg-1">
          <h3 class="text-lg font-bold text-gray-900">Light Sky Blue</h3>
          <p class="text-gray-700">Background Secondary</p>
          <code class="text-sm">#f0f9ff</code>
        </div>
        
        <div class="color-demo section-bg-2">
          <h3 class="text-lg font-bold text-gray-900">Light Yellow</h3>
          <p class="text-gray-700">Background Tertiary</p>
          <code class="text-sm">#fef3c7</code>
        </div>
        
        <div class="color-demo section-bg-3">
          <h3 class="text-lg font-bold text-gray-900">Light Green</h3>
          <p class="text-gray-700">Background Quaternary</p>
          <code class="text-sm">#ecfdf5</code>
        </div>
        
        <div class="color-demo section-bg-4">
          <h3 class="text-lg font-bold text-gray-900">Light Pink</h3>
          <p class="text-gray-700">Background Accent</p>
          <code class="text-sm">#fdf2f8</code>
        </div>
      </div>
    </section>

    <!-- Before/After Comparison -->
    <section class="mb-16">
      <h2 class="text-4xl font-bold text-center mb-8 text-gray-900">Before vs After Comparison</h2>
      
      <div class="comparison-grid">
        <div class="before">
          <h3 class="text-xl font-bold mb-4">Before: Gray Theme</h3>
          <p class="mb-4">Dull gray backgrounds and muted text colors made the website appear less engaging.</p>
          <div style="background: #f8fafc; color: #475569; padding: 1rem; border-radius: 0.5rem;">
            <p>Sample text in old gray scheme</p>
          </div>
        </div>
        
        <div class="after">
          <h3 class="text-xl font-bold mb-4">After: Bright Theme</h3>
          <p class="mb-4">Vibrant colors and enhanced contrast create a more engaging and professional appearance.</p>
          <div class="bg-gray-50 text-gray-700 p-4 rounded-lg">
            <p>Sample text in new bright scheme</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Feature Showcase -->
    <section class="mb-16">
      <h2 class="text-4xl font-bold text-center mb-8 text-gray-900">Enhanced Features</h2>
      
      <div class="feature-showcase">
        <!-- Enhanced Cards -->
        <div class="card">
          <div class="card-body">
            <h3 class="card-title">Enhanced Cards</h3>
            <p class="text-gray-600 mb-4">Cards now feature gradient backgrounds, colorful top borders, and improved hover effects.</p>
            <button class="btn btn-primary">Learn More</button>
          </div>
        </div>

        <!-- Improved Navigation -->
        <div class="card">
          <div class="card-body">
            <h3 class="card-title">Bright Navigation</h3>
            <p class="text-gray-600 mb-4">Navigation links now have colorful hover states with background highlights and smooth transitions.</p>
            <div class="space-x-2">
              <a href="#" class="nav-link inline-block">Home</a>
              <a href="#" class="nav-link inline-block">About</a>
              <a href="#" class="nav-link inline-block">Programs</a>
            </div>
          </div>
        </div>

        <!-- Enhanced Buttons -->
        <div class="card">
          <div class="card-body">
            <h3 class="card-title">Gradient Buttons</h3>
            <p class="text-gray-600 mb-4">Buttons now feature beautiful gradients, enhanced shadows, and engaging hover animations.</p>
            <div class="space-y-2">
              <button class="btn btn-primary w-full">Primary</button>
              <button class="btn btn-secondary w-full">Secondary</button>
              <button class="btn btn-accent w-full">Accent</button>
            </div>
          </div>
        </div>

        <!-- Color Accessibility -->
        <div class="card">
          <div class="card-body">
            <h3 class="card-title">Accessibility Maintained</h3>
            <p class="text-gray-600 mb-4">All color changes maintain proper contrast ratios and accessibility standards.</p>
            <div class="bg-gray-100 p-3 rounded text-center">
              <span class="text-gray-700 font-semibold">WCAG AA Compliant</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Text Color Examples -->
    <section class="mb-16">
      <h2 class="text-4xl font-bold text-center mb-8 text-gray-900">Enhanced Text Colors</h2>
      
      <div class="bg-gray-50 p-8 rounded-lg">
        <h3 class="text-2xl font-bold text-gray-900 mb-4">Primary Text (Deep Blue)</h3>
        <p class="text-gray-700 text-lg mb-4">Secondary text now uses bright blue instead of dull gray for better engagement.</p>
        <p class="text-gray-600 mb-4">Muted text uses green tones to add warmth and vibrancy to the content.</p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div class="bg-gray-100 p-4 rounded">
            <h4 class="text-lg font-bold text-gray-800 mb-2">Section 1</h4>
            <p class="text-gray-600">Content with enhanced readability and visual appeal.</p>
          </div>
          <div class="bg-gray-100 p-4 rounded">
            <h4 class="text-lg font-bold text-gray-800 mb-2">Section 2</h4>
            <p class="text-gray-600">Bright colors make content more engaging and professional.</p>
          </div>
          <div class="bg-gray-100 p-4 rounded">
            <h4 class="text-lg font-bold text-gray-800 mb-2">Section 3</h4>
            <p class="text-gray-600">Maintained accessibility while improving visual impact.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Implementation Summary -->
    <section class="mb-16">
      <h2 class="text-4xl font-bold text-center mb-8 text-gray-900">Implementation Summary</h2>
      
      <div class="bg-gray-50 p-8 rounded-lg">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-bold text-gray-800 mb-4">✅ What Changed</h3>
            <ul class="space-y-2 text-gray-700">
              <li>• Gray backgrounds → Bright pastels (sky blue, yellow, green, pink)</li>
              <li>• Gray text → Deep blue and bright blue text</li>
              <li>• Plain buttons → Gradient buttons with animations</li>
              <li>• Simple cards → Enhanced cards with colorful borders</li>
              <li>• Basic navigation → Interactive navigation with hover effects</li>
              <li>• Flat hero → Gradient hero with overlay effects</li>
            </ul>
          </div>
          
          <div>
            <h3 class="text-xl font-bold text-gray-800 mb-4">🎯 What Stayed</h3>
            <ul class="space-y-2 text-gray-700">
              <li>• Pink (#fc5c7d) and Purple (#6a82fb) brand colors</li>
              <li>• Dark mode functionality (unchanged)</li>
              <li>• Accessibility standards (WCAG AA compliance)</li>
              <li>• Professional appearance and layout</li>
              <li>• Mobile responsiveness</li>
              <li>• Theme toggle functionality</li>
            </ul>
          </div>
        </div>
        
        <div class="mt-8 text-center">
          <p class="text-lg text-gray-700 mb-4">
            <strong>Result:</strong> A more vibrant, engaging website that maintains professionalism while significantly improving visual appeal in light mode.
          </p>
          <div class="space-x-4">
            <a href="index.html" class="btn btn-primary">View Home Page</a>
            <a href="about.html" class="btn btn-secondary">View About Page</a>
            <a href="programs.html" class="btn btn-accent">View Programs</a>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- Scripts -->
  <script src="js/main.js"></script>
  
  <script>
    // Add some interactive demonstrations
    document.addEventListener('DOMContentLoaded', function() {
      // Animate color demos on scroll
      const colorDemos = document.querySelectorAll('.color-demo');
      
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.animation = 'slideInUp 0.6s ease-out';
          }
        });
      });
      
      colorDemos.forEach(demo => observer.observe(demo));
      
      // Add click effects to cards
      const cards = document.querySelectorAll('.card');
      cards.forEach(card => {
        card.addEventListener('click', function() {
          this.style.transform = 'translateY(-8px) scale(1.02)';
          setTimeout(() => {
            this.style.transform = '';
          }, 200);
        });
      });
    });
  </script>
</body>
</html>
