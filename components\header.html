<!-- Header Component -->
<header class="bg-white dark:bg-slate-800 shadow-md sticky top-0 z-50 transition-colors duration-300">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <a href="index.html" class="flex items-center">
        <h1 class="text-xl sm:text-2xl font-bold text-pink-600 dark:text-pink-400">Sisters for Good</h1>
      </a>

      <!-- Desktop Navigation (hidden on mobile) -->
      <nav class="hidden md:flex space-x-6">
        <a href="index.html" class="nav-link text-slate-800 dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Home</a>
        <a href="about.html" class="nav-link text-slate-800 dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">About Us</a>
        <a href="programs.html" class="nav-link text-slate-800 dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Programs</a>
        <a href="events.html" class="nav-link text-slate-800 dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Events</a>
        <a href="health.html" class="nav-link text-slate-800 dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Health Resources</a>
        <a href="donate.html" class="nav-link text-slate-800 dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Donate</a>
        <a href="contact.html" class="nav-link text-slate-800 dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Contact</a>
      </nav>

      <!-- Mobile menu button -->
      <button id="mobileMenuBtn" class="md:hidden flex items-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-600 dark:text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation (hidden by default) -->
    <div id="mobileMenu" class="md:hidden hidden mt-4 pb-4 border-t border-gray-200 dark:border-gray-600 pt-3">
      <nav class="flex flex-col space-y-4">
        <a href="index.html" class="nav-link text-center py-2 text-slate-800 dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Home</a>
        <a href="about.html" class="nav-link text-center py-2 text-slate-800 dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">About Us</a>
        <a href="programs.html" class="nav-link text-center py-2 text-slate-800 dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Programs</a>
        <a href="events.html" class="nav-link text-center py-2 text-slate-800 dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Events</a>
        <a href="health.html" class="nav-link text-center py-2 text-slate-800 dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Health Resources</a>
        <a href="donate.html" class="nav-link text-center py-2 text-slate-800 dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Donate</a>
        <a href="contact.html" class="nav-link text-center py-2 text-slate-800 dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Contact</a>
      </nav>
    </div>
  </div>
</header>
