<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Learn about Sisters for Good's 10+ year mission empowering women through cosmetology education, mentorship, and community support in Pottstown, PA.">
  <meta name="keywords" content="about Sisters for Good, women empowerment organization, <PERSON><PERSON> founder, Pottstown PA nonprofit, community development, women's education">
  <meta name="author" content="Sisters for Good">
  <meta name="robots" content="index, follow">
  <title>About Us | Sisters for Good - Our Mission & Team</title>

  <!-- Canonical URL -->
  <link rel="canonical" href="https://sistersforgood.org/about.html">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="About Us | Sisters for Good - Our Mission & Team">
  <meta property="og:description" content="Learn about Sisters for Good's 10+ year mission empowering women through cosmetology education, mentorship, and community support in Pottstown, PA.">
  <meta property="og:image" content="https://sistersforgood.org/assets/sfg_team_social.png">
  <meta property="og:url" content="https://sistersforgood.org/about.html">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Sisters for Good">

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="About Us | Sisters for Good - Our Mission & Team">
  <meta name="twitter:description" content="Learn about Sisters for Good's 10+ year mission empowering women through cosmetology education, mentorship, and community support in Pottstown, PA.">
  <meta name="twitter:image" content="https://sistersforgood.org/assets/sfg_team_social.png">

  <!-- Favicon -->
  <link rel="icon" href="favicon.svg" type="image/svg+xml">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <link rel="apple-touch-icon" href="favicon.svg">

  <!-- Font Loading Optimizer (prevents header size jumping) -->
  <script src="js/font-loading-optimizer.js"></script>

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Custom CSS -->
  <link href="css/main.css" rel="stylesheet">

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#fc5c7d',
            secondary: '#6a82fb',
            accent: '#ffee02'
          },
          fontFamily: {
            sans: ['Poppins', 'sans-serif'],
            body: ['Roboto', 'sans-serif']
          }
        }
      }
    }
  </script>
</head>
<body>

<!-- Header Component -->
<header class="bg-white dark:bg-slate-800 shadow-md sticky top-0 z-50 transition-colors duration-300">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <a href="index.html" class="flex items-center">
        <h1 class="text-xl sm:text-2xl font-bold text-pink-600 dark:text-pink-400">Sisters for Good</h1>
      </a>

      <!-- Desktop Navigation (hidden on mobile) -->
      <nav class="hidden md:flex space-x-6">
        <a href="index.html" class="nav-link text-white dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Home</a>
        <a href="about.html" class="nav-link text-pink-600 dark:text-pink-400 font-semibold">About Us</a>
        <a href="programs.html" class="nav-link text-white dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Programs</a>
        <a href="events.html" class="nav-link text-white dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Events</a>
        <a href="health.html" class="nav-link text-white dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Health Resources</a>
        <a href="donate.html" class="nav-link text-white dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Donate</a>
        <a href="contact.html" class="nav-link text-white dark:text-slate-200 hover:text-pink-600 dark:hover:text-pink-400 transition-colors font-medium">Contact</a>
      </nav>

      <!-- Theme Toggle Button -->
      <button id="theme-toggle" class="theme-toggle hidden md:flex" aria-label="Toggle dark mode" title="Toggle dark/light theme">
        <svg class="sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
        </svg>
        <svg class="moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
        </svg>
      </button>

      <!-- Mobile menu button -->
      <button id="mobileMenuBtn" class="md:hidden flex items-center p-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-pink-600 dark:text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation (hidden by default) -->
    <div id="mobileMenu" class="md:hidden hidden mt-4 pb-4 border-t border-gray-200 dark:border-gray-600 pt-3">
      <nav class="flex flex-col space-y-4">
        <a href="index.html" class="nav-link text-center py-2 text-white dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Home</a>
        <a href="about.html" class="nav-link text-center py-2 bg-pink-50 dark:bg-gray-700 text-pink-600 dark:text-pink-400 font-medium rounded-md">About Us</a>
        <a href="programs.html" class="nav-link text-center py-2 text-white dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Programs</a>
        <a href="events.html" class="nav-link text-center py-2 text-white dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Events</a>
        <a href="health.html" class="nav-link text-center py-2 text-white dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Health Resources</a>
        <a href="donate.html" class="nav-link text-center py-2 text-white dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Donate</a>
        <a href="contact.html" class="nav-link text-center py-2 text-white dark:text-slate-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-pink-600 dark:hover:text-pink-400 transition-colors rounded-md font-medium">Contact</a>
      </nav>
    </div>
  </div>
</header>

<!-- Page Header -->
<section class="bg-gradient-to-r from-teal-500 to-blue-600 text-white py-16 relative overflow-hidden">
  <div class="absolute inset-0 bg-black opacity-10"></div>
  <div class="container mx-auto px-4 text-center relative z-10">
    <h1 class="text-4xl md:text-5xl font-bold mb-4 drop-shadow-lg" data-aos="fade-up">About Sisters for Good</h1>
    <p class="text-xl max-w-3xl mx-auto leading-relaxed drop-shadow-md" data-aos="fade-up" data-aos-delay="100">Empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach.</p>
    <div class="mt-8 flex justify-center space-x-2" data-aos="fade-up" data-aos-delay="200">
      <div class="w-3 h-3 bg-white rounded-full opacity-60 animate-pulse"></div>
      <div class="w-3 h-3 bg-white rounded-full opacity-80 animate-pulse" style="animation-delay: 0.2s;"></div>
      <div class="w-3 h-3 bg-white rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
    </div>
  </div>
</section>

<!-- Our Story Section -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Our Story</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
      <div data-aos="fade-right">
        <img src="https://images.unsplash.com/photo-1633329712165-4e578376eb87?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fHdvbWVuJTIwZW1wb3dlcm1lbnR8ZW58MHx8MHx8fDA%3D" alt="Sisters for Good Team" class="rounded-lg shadow-lg w-full">
      </div>
      <div data-aos="fade-left">
        <p class="mb-4">Sisters for Good was founded over 10 years ago with a simple yet powerful vision: to create a supportive community where women could thrive, learn, and grow together.</p>
        <p class="mb-4">What began as small gatherings in Pottstown, Pennsylvania has grown into a vibrant organization that touches the lives of hundreds of women and their families each year.</p>
        <p>Our founder, Sheryl Williams, a passionate businesswoman and community builder, recognized the need for programs that specifically addressed the challenges faced by women in our communities. Through her leadership and the dedication of our volunteers, Sisters for Good has become a beacon of hope and empowerment.</p>
      </div>
    </div>
  </div>
</section>

<!-- Mission & Vision Section -->
<section class="section section-bg-2">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Mission -->
      <div class="card" data-aos="fade-up">
        <div class="card-body">
          <h3 class="text-2xl font-bold mb-4">Our Mission</h3>
          <p>Our mission is to create sustainable programs that empower women economically, emotionally, and professionally while building strong community bonds.</p>
          <p class="mt-4">We believe that when women are supported and empowered, entire communities benefit and thrive.</p>
        </div>
      </div>

      <!-- Vision -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="card-body">
          <h3 class="text-2xl font-bold mb-4">Our Vision</h3>
          <p>We envision a world where all women have access to the resources, support, and opportunities they need to reach their full potential.</p>
          <p class="mt-4">Through education, mentorship, and community engagement, we work to make this vision a reality.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Core Values Section -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Our Core Values</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Value 1 -->
      <div class="card" data-aos="fade-up">
        <div class="card-body text-center">
          <div class="text-primary text-4xl mb-4">❤️</div>
          <h3 class="text-xl font-bold mb-2">Compassion</h3>
          <p>We approach our work with empathy and understanding, recognizing the unique challenges faced by each individual.</p>
        </div>
      </div>

      <!-- Value 2 -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="card-body text-center">
          <div class="text-primary text-4xl mb-4">🤝</div>
          <h3 class="text-xl font-bold mb-2">Collaboration</h3>
          <p>We believe in the power of working together, pooling resources, and sharing knowledge to achieve greater impact.</p>
        </div>
      </div>

      <!-- Value 3 -->
      <div class="card" data-aos="fade-up" data-aos-delay="200">
        <div class="card-body text-center">
          <div class="text-primary text-4xl mb-4">💪</div>
          <h3 class="text-xl font-bold mb-2">Empowerment</h3>
          <p>We focus on building skills, confidence, and independence, enabling women to create positive change in their own lives.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="section">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Success Stories</h2>
    </div>
    <div class="max-w-3xl mx-auto" data-aos="fade-up">
      <div id="testimonials-container">
        <!-- Testimonials component will be loaded here -->
      </div>
    </div>
  </div>
</section>

<!-- Team Section -->
<section class="section section-bg-3">
  <div class="container mx-auto px-4">
    <div class="section-title">
      <h2 data-aos="fade-up">Our Team</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Team Member 1 -->
      <div class="card" data-aos="fade-up">
        <div class="card-body text-center">
          <div class="w-32 h-32 rounded-full overflow-hidden mx-auto mb-4">
            <img src="https://images.unsplash.com/photo-1523289217630-0dd16184af8e?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTl8fHdvbWVuJTIwZW1wb3dlcm1lbnR8ZW58MHx8MHx8fDA%3D" alt="Sheryl Williams" class="w-full h-full object-cover">
          </div>
          <h3 class="text-xl font-bold mb-1">Sheryl Williams</h3>
          <p class="text-primary mb-3">Founder & Executive Director</p>
          <p>A passionate advocate for women's empowerment with over 20 years of experience in community development.</p>
        </div>
      </div>

      <!-- Team Member 2 -->
      <div class="card" data-aos="fade-up" data-aos-delay="100">
        <div class="card-body text-center">
          <div class="w-32 h-32 rounded-full overflow-hidden mx-auto mb-4">
            <img src="https://images.unsplash.com/photo-1506782081254-09bcfd996fd6?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fHdvbWVuJTIwZW1wb3dlcm1lbnR8ZW58MHx8MHx8fDA%3D" alt="Barbara Lee Stanislaus" class="w-full h-full object-cover">
          </div>
          <h3 class="text-xl font-bold mb-1">Barbara Lee Stanislaus</h3>
          <p class="text-primary mb-3">Program Director</p>
          <p>Leads our educational initiatives and brings extensive experience in curriculum development and teaching.</p>
        </div>
      </div>

      <!-- Team Member 3 -->
      <div class="card" data-aos="fade-up" data-aos-delay="200">
        <div class="card-body text-center">
          <div class="w-32 h-32 rounded-full overflow-hidden mx-auto mb-4">
            <img src="https://images.unsplash.com/photo-1637072103875-1b29a09d9c91?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fHdvbWVuJTIwZW1wb3dlcm1lbnR8ZW58MHx8MHx8fDA%3D" alt="Michelle Johnson" class="w-full h-full object-cover">
          </div>
          <h3 class="text-xl font-bold mb-1">Michelle Johnson</h3>
          <p class="text-primary mb-3">Community Outreach Coordinator</p>
          <p>Builds and maintains relationships with local schools, businesses, and community organizations.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="section bg-primary text-white text-center">
  <div class="container mx-auto px-4">
    <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">Join Our Mission</h2>
    <p class="max-w-2xl mx-auto mb-8" data-aos="fade-up" data-aos-delay="100">Whether you're looking to volunteer, donate, or participate in our programs, there are many ways to get involved with Sisters for Good.</p>
    <div class="flex flex-wrap justify-center gap-4" data-aos="fade-up" data-aos-delay="200">
      <a href="contact.html" class="btn btn-accent text-dark-color">Contact Us</a>
      <a href="donate.html" class="btn btn-light">Donate Now</a>
    </div>
  </div>
</section>

<!-- Footer Component -->
<footer class="bg-gray-800 text-white py-10">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Organization Info -->
      <div>
        <h3 class="text-xl font-bold mb-4">Sisters for Good</h3>
        <p class="mb-2">Empowering women and strengthening communities through collaborative initiatives, support programs, and educational outreach.</p>
        <p class="text-sm mt-4">&copy; 2025 Sisters for Good. All Rights Reserved.</p>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-xl font-bold mb-4">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="index.html" class="hover:text-pink-400 transition-colors">Home</a></li>
          <li><a href="about.html" class="hover:text-pink-400 transition-colors">About Us</a></li>
          <li><a href="programs.html" class="hover:text-pink-400 transition-colors">Programs</a></li>
          <li><a href="events.html" class="hover:text-pink-400 transition-colors">Events</a></li>
          <li><a href="health.html" class="hover:text-pink-400 transition-colors">Health Resources</a></li>
          <li><a href="donate.html" class="hover:text-pink-400 transition-colors">Donate</a></li>
          <li><a href="contact.html" class="hover:text-pink-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Information -->
      <div>
        <h3 class="text-xl font-bold mb-4">Contact Us</h3>
        <address class="not-italic">
          <p class="mb-2">📍 247 East High St, Pottstown, PA 19464</p>
          <p class="mb-2">📞 (201) 403-7417</p>
          <p class="mb-2">📧 <a href="mailto:<EMAIL>" class="hover:text-pink-400 transition-colors"><EMAIL></a></p>
        </address>

        <!-- Social Media Links -->
        <div class="mt-4 flex space-x-4">
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Facebook</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Instagram</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="#" class="text-white hover:text-pink-400 transition-colors">
            <span class="sr-only">Twitter</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>

<!-- Scripts -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="js/main.js"></script>
<script src="js/search.js"></script>
<script src="js/mobile-menu-enhanced.js"></script>
<script src="js/accessibility.js"></script>
<script src="js/performance.js"></script>
<script src="js/analytics-monitoring.js"></script>
<script src="js/seo-monitoring.js"></script>

<!-- Initialize Scripts -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile menu
    initMobileMenu();

    // Set active nav link
    setActiveNavLink();
  });
</script>
</body>
</html>
