<!-- Functional Contact Form Component -->
<div class="contact-form-container bg-white dark:bg-slate-700 rounded-lg shadow-md overflow-hidden transition-colors duration-300">
  <div class="p-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white">
    <h3 class="text-xl font-bold">Contact Us</h3>
    <p class="text-sm">Fill out this form and we'll get back to you as soon as possible.</p>
  </div>
  
  <div class="p-6">
    <form id="contact-form" action="https://formspree.io/f/xpznvqpb" method="POST" class="space-y-6">
      <!-- Name Fields -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="firstName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name *</label>
          <input type="text" id="firstName" name="firstName" required
                 class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-slate-600 dark:text-white transition-colors duration-300">
        </div>
        <div>
          <label for="lastName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name *</label>
          <input type="text" id="lastName" name="lastName" required
                 class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-slate-600 dark:text-white transition-colors duration-300">
        </div>
      </div>

      <!-- Email -->
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address *</label>
        <input type="email" id="email" name="email" required
               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-slate-600 dark:text-white transition-colors duration-300">
      </div>

      <!-- Phone -->
      <div>
        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number</label>
        <input type="tel" id="phone" name="phone"
               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-slate-600 dark:text-white transition-colors duration-300">
      </div>

      <!-- Subject -->
      <div>
        <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Subject *</label>
        <select id="subject" name="subject" required
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-slate-600 dark:text-white transition-colors duration-300">
          <option value="">Select a subject</option>
          <option value="general">General Inquiry</option>
          <option value="volunteer">Volunteer Opportunities</option>
          <option value="programs">Program Information</option>
          <option value="partnership">Partnership Opportunities</option>
          <option value="donation">Donation Questions</option>
          <option value="event">Event Information</option>
          <option value="other">Other</option>
        </select>
      </div>

      <!-- Message -->
      <div>
        <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Message *</label>
        <textarea id="message" name="message" rows="5" required
                  placeholder="Please share your message, questions, or how you'd like to get involved with Sisters for Good..."
                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-slate-600 dark:text-white transition-colors duration-300"></textarea>
      </div>

      <!-- Hidden fields for form identification -->
      <input type="hidden" name="_subject" value="New Contact Form Submission - Sisters for Good">
      <input type="hidden" name="_next" value="https://sistersforgood.org/contact.html?success=true">
      <input type="hidden" name="_captcha" value="false">

      <!-- Submit Button -->
      <button type="submit" id="submit-btn"
              class="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed">
        <span id="submit-text">Send Message</span>
        <span id="submit-loading" class="hidden">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Sending...
        </span>
      </button>
    </form>

    <!-- Success Message -->
    <div id="success-message" class="hidden mt-6 p-4 bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 rounded-lg">
      <h4 class="font-bold">✅ Message Sent Successfully!</h4>
      <p>Thank you for contacting Sisters for Good! We'll get back to you within 24-48 hours.</p>
      <p class="mt-2"><strong>Need immediate assistance?</strong> Call us at <a href="tel:************" class="text-green-600 dark:text-green-400 hover:underline">(*************</a></p>
    </div>

    <!-- Error Message -->
    <div id="error-message" class="hidden mt-6 p-4 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 rounded-lg">
      <h4 class="font-bold">❌ Error Sending Message</h4>
      <p>We're sorry, but there was an error sending your message. Please try again or contact us directly.</p>
      <p class="mt-2"><strong>Alternative contact:</strong> Email us at <a href="mailto:<EMAIL>" class="text-red-600 dark:text-red-400 hover:underline"><EMAIL></a></p>
    </div>
  </div>
</div>

<script>
// Contact Form Handling
document.addEventListener('DOMContentLoaded', function() {
  const contactForm = document.getElementById('contact-form');
  const submitBtn = document.getElementById('submit-btn');
  const submitText = document.getElementById('submit-text');
  const submitLoading = document.getElementById('submit-loading');
  const successMessage = document.getElementById('success-message');
  const errorMessage = document.getElementById('error-message');

  // Check for success parameter in URL
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('success') === 'true') {
    showSuccessMessage();
  }

  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Show loading state
      submitBtn.disabled = true;
      submitText.classList.add('hidden');
      submitLoading.classList.remove('hidden');
      
      // Hide previous messages
      successMessage.classList.add('hidden');
      errorMessage.classList.add('hidden');

      // Prepare form data
      const formData = new FormData(contactForm);
      
      // Submit to Formspree
      fetch(contactForm.action, {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json'
        }
      })
      .then(response => {
        if (response.ok) {
          showSuccessMessage();
          contactForm.reset();
        } else {
          throw new Error('Form submission failed');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showErrorMessage();
      })
      .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        submitLoading.classList.add('hidden');
      });
    });
  }

  function showSuccessMessage() {
    successMessage.classList.remove('hidden');
    successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  function showErrorMessage() {
    errorMessage.classList.remove('hidden');
    errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
});
</script>
