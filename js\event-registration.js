/**
 * Sisters for Good - Event Registration
 * Handles event registration functionality
 */

document.addEventListener('DOMContentLoaded', function() {
  initEventRegistration();
});

function initEventRegistration() {
  // Load the registration modal
  loadRegistrationModal();
  
  // Add event listeners to registration buttons
  const registerButtons = document.querySelectorAll('.event-register-btn');
  
  registerButtons.forEach(button => {
    button.addEventListener('click', function() {
      const eventId = this.getAttribute('data-event-id');
      const eventName = this.getAttribute('data-event-name');
      openRegistrationModal(eventId, eventName);
    });
  });
}

function loadRegistrationModal() {
  const modalContainer = document.createElement('div');
  modalContainer.id = 'registration-modal-container';
  document.body.appendChild(modalContainer);
  
  // Fetch registration modal component
  fetch('components/event-registration.html')
    .then(response => response.text())
    .then(data => {
      modalContainer.innerHTML = data;
      
      // Add event listeners to modal buttons
      const closeButton = document.getElementById('close-modal');
      const cancelButton = document.getElementById('cancel-registration');
      const registrationForm = document.getElementById('event-registration-form');
      
      if (closeButton) {
        closeButton.addEventListener('click', closeRegistrationModal);
      }
      
      if (cancelButton) {
        cancelButton.addEventListener('click', closeRegistrationModal);
      }
      
      if (registrationForm) {
        registrationForm.addEventListener('submit', handleRegistrationSubmit);
      }
    })
    .catch(error => {
      console.error('Error loading registration modal:', error);
    });
}

function openRegistrationModal(eventId, eventName) {
  const modal = document.getElementById('event-registration-modal');
  const eventIdField = document.getElementById('event-id');
  const eventTitleElement = document.getElementById('modal-event-title');
  
  if (modal && eventIdField && eventTitleElement) {
    // Set event details
    eventIdField.value = eventId;
    eventTitleElement.textContent = `Register for: ${eventName}`;
    
    // Show modal
    modal.classList.remove('hidden');
    
    // Prevent body scrolling
    document.body.style.overflow = 'hidden';
  }
}

function closeRegistrationModal() {
  const modal = document.getElementById('event-registration-modal');
  const form = document.getElementById('event-registration-form');
  
  if (modal) {
    modal.classList.add('hidden');
    
    // Allow body scrolling again
    document.body.style.overflow = '';
    
    // Reset form if it exists
    if (form) {
      form.reset();
    }
  }
}

function handleRegistrationSubmit(e) {
  e.preventDefault();
  
  // Get form data
  const formData = new FormData(e.target);
  const formDataObj = {};
  formData.forEach((value, key) => {
    formDataObj[key] = value;
  });
  
  // In a real implementation, you would send this data to a server
  // For now, we'll just simulate a successful registration
  
  // Simulate API call
  setTimeout(() => {
    // Show success message
    alert(`Thank you for registering for this event! A confirmation email will be sent to ${formDataObj.email}.`);
    
    // Close modal
    closeRegistrationModal();
  }, 1000);
}
