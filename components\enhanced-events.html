<!-- Enhanced Events Component with Registration System -->
<div id="enhanced-events-container" class="space-y-8">
  <!-- Events Filter and Search -->
  <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 transition-colors duration-300">
    <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
      <div class="flex-1">
        <input type="text" id="event-search" placeholder="Search events..." 
               class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
      </div>
      <div class="flex gap-2">
        <select id="event-category-filter" class="p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
          <option value="">All Categories</option>
          <option value="community">Community</option>
          <option value="service">Service</option>
          <option value="education">Education</option>
          <option value="health">Health</option>
        </select>
        <select id="event-time-filter" class="p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white">
          <option value="">All Time</option>
          <option value="upcoming">Upcoming</option>
          <option value="this-month">This Month</option>
          <option value="next-month">Next Month</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Events Grid -->
  <div id="events-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Events will be dynamically loaded here -->
  </div>

  <!-- No Events Message -->
  <div id="no-events-message" class="hidden text-center py-12">
    <div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">📅</div>
    <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">No events found</h3>
    <p class="text-gray-500 dark:text-gray-500">Try adjusting your search or filter criteria.</p>
  </div>
</div>

<script>
class EnhancedEventsComponent {
  constructor() {
    this.events = [];
    this.filteredEvents = [];
    this.init();
  }

  init() {
    this.loadEvents();
    this.setupEventListeners();
    this.renderEvents();
  }

  loadEvents() {
    // Get events from the event management system
    if (window.eventManagement) {
      this.events = window.eventManagement.events;
    } else {
      // Fallback events if event management system isn't loaded
      this.events = [
        {
          id: 'fathers-day-2025',
          title: "Father's Day Brunch",
          description: "Join us for our annual Father's Day celebration with food, fellowship, and family fun.",
          date: '2025-06-15',
          time: '10:00',
          endTime: '11:00',
          location: '247 East High Street, Pottstown, PA 19464',
          capacity: 50,
          registered: 12,
          category: 'community',
          image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&q=80',
          requiresRSVP: true,
          price: 'Free',
          tags: ['family', 'community', 'food', 'celebration']
        }
      ];
    }
    this.filteredEvents = [...this.events];
  }

  setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('event-search');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.filterEvents();
      });
    }

    // Category filter
    const categoryFilter = document.getElementById('event-category-filter');
    if (categoryFilter) {
      categoryFilter.addEventListener('change', () => {
        this.filterEvents();
      });
    }

    // Time filter
    const timeFilter = document.getElementById('event-time-filter');
    if (timeFilter) {
      timeFilter.addEventListener('change', () => {
        this.filterEvents();
      });
    }
  }

  filterEvents() {
    const searchTerm = document.getElementById('event-search')?.value.toLowerCase() || '';
    const categoryFilter = document.getElementById('event-category-filter')?.value || '';
    const timeFilter = document.getElementById('event-time-filter')?.value || '';

    this.filteredEvents = this.events.filter(event => {
      // Search filter
      const matchesSearch = !searchTerm || 
        event.title.toLowerCase().includes(searchTerm) ||
        event.description.toLowerCase().includes(searchTerm) ||
        event.tags.some(tag => tag.toLowerCase().includes(searchTerm));

      // Category filter
      const matchesCategory = !categoryFilter || event.category === categoryFilter;

      // Time filter
      const matchesTime = this.matchesTimeFilter(event, timeFilter);

      return matchesSearch && matchesCategory && matchesTime;
    });

    this.renderEvents();
  }

  matchesTimeFilter(event, timeFilter) {
    if (!timeFilter) return true;

    const eventDate = new Date(event.date);
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const startOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    const endOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 2, 0);

    switch (timeFilter) {
      case 'upcoming':
        return eventDate >= now;
      case 'this-month':
        return eventDate >= startOfMonth && eventDate <= endOfMonth;
      case 'next-month':
        return eventDate >= startOfNextMonth && eventDate <= endOfNextMonth;
      default:
        return true;
    }
  }

  renderEvents() {
    const eventsGrid = document.getElementById('events-grid');
    const noEventsMessage = document.getElementById('no-events-message');

    if (!eventsGrid) return;

    if (this.filteredEvents.length === 0) {
      eventsGrid.innerHTML = '';
      noEventsMessage?.classList.remove('hidden');
      return;
    }

    noEventsMessage?.classList.add('hidden');

    eventsGrid.innerHTML = this.filteredEvents.map(event => this.createEventCard(event)).join('');
  }

  createEventCard(event) {
    const eventDate = new Date(event.date);
    const isUpcoming = eventDate >= new Date();
    const spotsLeft = event.capacity - event.registered;
    const isFull = spotsLeft <= 0;

    return `
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1" data-event-id="${event.id}">
        <!-- Event Image -->
        <div class="relative h-48 overflow-hidden">
          <img src="${event.image}" alt="${event.title}" class="w-full h-full object-cover transition-transform duration-300 hover:scale-105" loading="lazy">
          <div class="absolute top-4 left-4">
            <span class="bg-${this.getCategoryColor(event.category)}-600 text-white px-3 py-1 rounded-full text-sm font-medium">
              ${this.formatCategory(event.category)}
            </span>
          </div>
          ${!isUpcoming ? '<div class="absolute top-4 right-4 bg-gray-600 text-white px-3 py-1 rounded-full text-sm">Past Event</div>' : ''}
        </div>

        <!-- Event Content -->
        <div class="p-6">
          <div class="flex items-start justify-between mb-3">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white line-clamp-2">${event.title}</h3>
            <span class="text-2xl ml-2">${this.getEventIcon(event.category)}</span>
          </div>

          <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">${event.description}</p>

          <!-- Event Details -->
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              ${this.formatEventDate(event)}
            </div>
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span class="line-clamp-1">${event.location}</span>
            </div>
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              ${event.price}
            </div>
          </div>

          <!-- Capacity Info -->
          ${event.requiresRSVP ? `
            <div class="mb-4">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Capacity</span>
                <span class="spots-remaining text-sm ${isFull ? 'text-red-600' : 'text-green-600'}">
                  ${isFull ? 'Event full' : `${spotsLeft} spots remaining`}
                </span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="bg-gradient-to-r from-pink-500 to-purple-600 h-2 rounded-full transition-all duration-300" 
                     style="width: ${(event.registered / event.capacity) * 100}%"></div>
              </div>
            </div>
          ` : ''}

          <!-- Event Tags -->
          <div class="flex flex-wrap gap-1 mb-4">
            ${event.tags.slice(0, 3).map(tag => `
              <span class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs">
                ${tag}
              </span>
            `).join('')}
            ${event.tags.length > 3 ? `<span class="text-gray-500 text-xs">+${event.tags.length - 3} more</span>` : ''}
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-2">
            ${isUpcoming && event.requiresRSVP ? `
              <button class="register-btn flex-1 bg-gradient-to-r from-pink-600 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-pink-700 hover:to-purple-700 transition-all duration-300 font-medium" 
                      data-event-id="${event.id}">
                ${isFull ? 'Join Waitlist' : 'Register'}
              </button>
            ` : ''}
            <button class="add-to-calendar-btn flex-shrink-0 bg-gray-600 text-white py-2 px-3 rounded-lg hover:bg-gray-700 transition-colors" 
                    data-event-id="${event.id}" title="Add to Calendar">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </button>
            <button class="share-event-btn flex-shrink-0 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors" 
                    data-event-id="${event.id}" title="Share Event">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  formatEventDate(event) {
    const date = new Date(event.date);
    const time = this.formatTime(event.time);
    return `${date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    })} at ${time}`;
  }

  formatTime(time) {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  }

  formatCategory(category) {
    return category.charAt(0).toUpperCase() + category.slice(1);
  }

  getCategoryColor(category) {
    const colors = {
      community: 'blue',
      service: 'green',
      education: 'purple',
      health: 'red'
    };
    return colors[category] || 'gray';
  }

  getEventIcon(category) {
    const icons = {
      community: '🤝',
      service: '🤲',
      education: '📚',
      health: '🏥'
    };
    return icons[category] || '📅';
  }
}

// Initialize enhanced events component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  if (document.getElementById('enhanced-events-container')) {
    window.enhancedEventsComponent = new EnhancedEventsComponent();
  }
});
</script>

<style>
/* Enhanced Events Component Styles */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hover effects for event cards */
.event-card:hover .event-image {
  transform: scale(1.05);
}

/* Loading animation for event cards */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: shimmer 1.2s ease-in-out infinite;
}
</style>
